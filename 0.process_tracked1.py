import pandas as pd
import numpy as np

def process_tracked1_data():
    """
    处理tracked1_extracted.csv数据，使用unstack将platform_name转换为列
    """
    print("开始处理tracked1_extracted数据...")
    
    # 读取数据
    df = pd.read_csv('output/tracked1_extracted.csv')
    print(f"原始数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 显示数据概览
    print("\n数据概览:")
    print(df.head())
    
    # 查看有哪些平台
    platforms = df['platform_name'].unique()
    print(f"\n平台列表 ({len(platforms)}个):")
    for i, platform in enumerate(platforms, 1):
        print(f"  {i}. {platform}")
    
    # 转换date列为datetime
    df['date'] = pd.to_datetime(df['date'])
    
    # 设置索引为date和platform_name，准备进行unstack
    print("\n开始数据重塑...")
    
    # 为了避免重复索引问题，先检查是否有重复的date-platform组合
    duplicates = df.groupby(['date', 'platform_name']).size()
    if duplicates.max() > 1:
        print("发现重复的date-platform组合，进行去重...")
        df = df.drop_duplicates(subset=['date', 'platform_name'], keep='last')
    
    # 选择要处理的数值列
    value_columns = ['incremental_number', 'total_number', 'tracked_number_percent']
    
    results = {}
    
    for col in value_columns:
        print(f"\n处理列: {col}")
        
        # 创建透视表，使用date作为索引，platform_name作为列
        pivot_df = df.pivot_table(
            index='date', 
            columns='platform_name', 
            values=col, 
            aggfunc='first'  # 如果有重复值，取第一个
        )
        
        # 填充缺失值
        pivot_df = pivot_df.fillna(0)
        
        print(f"  转换后形状: {pivot_df.shape}")
        print(f"  列名: {list(pivot_df.columns)}")
        
        # 保存结果
        output_file = f'output/tracked1_{col}_pivoted.csv'
        pivot_df.to_csv(output_file)
        print(f"  已保存到: {output_file}")
        
        results[col] = pivot_df
        
        # 显示前几行预览
        print(f"  数据预览:")
        print(pivot_df.head(3))
    
    # 创建一个综合的Excel文件，包含所有指标
    print("\n创建综合Excel文件...")
    with pd.ExcelWriter('output/tracked1_all_metrics_pivoted.xlsx', engine='openpyxl') as writer:
        for col, data in results.items():
            sheet_name = col.replace('_', ' ').title()
            data.to_excel(writer, sheet_name=sheet_name)
            print(f"  已添加工作表: {sheet_name}")
    
    print("\n✅ 处理完成！")
    print("生成的文件:")
    for col in value_columns:
        print(f"  - output/tracked1_{col}_pivoted.csv")
    print("  - output/tracked1_all_metrics_pivoted.xlsx")
    
    return results

def analyze_platform_trends(results):
    """
    分析平台趋势
    """
    print("\n" + "="*60)
    print("平台趋势分析")
    print("="*60)
    
    for metric, data in results.items():
        print(f"\n📊 {metric.replace('_', ' ').title()} 分析:")
        
        # 计算最新值
        latest_data = data.iloc[-1]
        top_platforms = latest_data.sort_values(ascending=False).head(5)
        
        print(f"  最新数据 ({data.index[-1].strftime('%Y-%m-%d')}):")
        for platform, value in top_platforms.items():
            print(f"    {platform}: {value:,.2f}")
        
        # 计算增长率（最近一年）
        if len(data) > 52:  # 假设是周数据
            year_ago_data = data.iloc[-53]
            growth_rates = ((latest_data - year_ago_data) / year_ago_data * 100).fillna(0)
            top_growth = growth_rates.sort_values(ascending=False).head(5)
            
            print(f"  年增长率 Top 5:")
            for platform, rate in top_growth.items():
                if not np.isinf(rate):
                    print(f"    {platform}: {rate:.2f}%")

if __name__ == "__main__":
    # 处理数据
    results = process_tracked1_data()
    
    # 分析趋势
    analyze_platform_trends(results) 