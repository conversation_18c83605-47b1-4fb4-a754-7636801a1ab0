#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并全部数据合并.xlsx与800中债.xlsx
"""

import pandas as pd
import numpy as np
from datetime import datetime

def load_and_clean_data():
    """
    加载并清理两个数据文件
    """
    print("正在读取全部数据合并.xlsx...")
    # 读取全部数据合并文件
    df_all = pd.read_excel('全部数据合并.xlsx')
    print(f"全部数据合并.xlsx shape: {df_all.shape}")
    print(f"日期范围: {df_all['Date'].min()} 至 {df_all['Date'].max()}")
    
    print("\n正在读取800中债.xlsx...")
    # 读取800中债文件，跳过前两行作为正确的表头
    df_bond = pd.read_excel('800中债.xlsx', header=2)
    
    # 清理800中债数据
    # 删除第一行（包含代码信息）
    df_bond = df_bond.iloc[1:].copy()
    
    # 确保日期列是datetime格式
    df_bond['日期'] = pd.to_datetime(df_bond['日期'])
    
    # 将数值列转换为数值格式
    for col in ['800收益', '中证全债']:
        df_bond[col] = pd.to_numeric(df_bond[col], errors='coerce')
    
    # 删除空值行
    df_bond = df_bond.dropna(subset=['日期'])
    
    print(f"800中债.xlsx shape: {df_bond.shape}")
    print(f"日期范围: {df_bond['日期'].min()} 至 {df_bond['日期'].max()}")
    
    return df_all, df_bond

def merge_datasets(df_all, df_bond):
    """
    合并两个数据集，并使用前置填充处理缺失值
    """
    print("\n开始合并数据...")
    
    # 确保全部数据的Date列也是datetime格式
    df_all['Date'] = pd.to_datetime(df_all['Date'])
    
    # 重命名800中债的列以便合并
    df_bond_renamed = df_bond.rename(columns={
        '日期': 'Date',
        '800收益': '中证800收益',
        '中证全债': '中证全债指数'
    })
    
    # 基于日期进行左连接
    merged_df = pd.merge(df_all, df_bond_renamed, on='Date', how='left')
    
    print(f"合并后数据shape: {merged_df.shape}")
    
    # 显示合并前的统计信息
    print("\n合并前新增列统计信息:")
    for col in ['中证800收益', '中证全债指数']:
        valid_count = merged_df[col].notna().sum()
        total_count = len(merged_df)
        print(f"{col}: {valid_count}/{total_count} ({valid_count/total_count*100:.1f}%) 有效数据")
    
    # 对新增的债券数据列进行前置填充
    print("\n正在对债券数据进行前置填充...")
    bond_columns = ['中证800收益', '中证全债指数']
    
    # 确保数据按日期排序
    merged_df = merged_df.sort_values('Date').reset_index(drop=True)
    
    # 对债券列进行前置填充
    merged_df[bond_columns] = merged_df[bond_columns].ffill()
    
    # 显示前置填充后的统计信息
    print("\n前置填充后新增列统计信息:")
    for col in bond_columns:
        valid_count = merged_df[col].notna().sum()
        total_count = len(merged_df)
        print(f"{col}: {valid_count}/{total_count} ({valid_count/total_count*100:.1f}%) 有效数据")
    
    return merged_df

def analyze_merge_results(merged_df):
    """
    分析合并结果
    """
    print("\n=== 合并结果分析 ===")
    
    # 检查日期覆盖情况
    print(f"合并后总行数: {len(merged_df)}")
    print(f"日期范围: {merged_df['Date'].min()} 至 {merged_df['Date'].max()}")
    
    # 检查新增数据的时间覆盖
    bond_data_mask = merged_df['中证800收益'].notna()
    if bond_data_mask.any():
        bond_dates = merged_df[bond_data_mask]['Date']
        print(f"债券数据覆盖时间: {bond_dates.min()} 至 {bond_dates.max()}")
    
    # 检查重叠期间的数据
    print("\n各列数据可用性:")
    for col in merged_df.columns:
        if col != 'Date':
            valid_count = merged_df[col].notna().sum()
            print(f"{col}: {valid_count} 个有效数据点")

def main():
    """
    主函数
    """
    print("=== 开始合并全部数据合并.xlsx与800中债.xlsx ===")
    
    try:
        # 加载数据
        df_all, df_bond = load_and_clean_data()
        
        # 合并数据
        merged_df = merge_datasets(df_all, df_bond)
        
        # 分析结果
        analyze_merge_results(merged_df)
        
        # 保存结果
        output_filename = '全部数据含债券合并.xlsx'
        print(f"\n正在保存合并结果到 {output_filename}...")
        merged_df.to_excel(output_filename, index=False)
        print(f"合并完成！文件已保存为: {output_filename}")
        
        # 显示前几行数据预览
        print("\n=== 合并结果预览 ===")
        print("前5行数据:")
        print(merged_df.head())
        
        print("\n最后5行数据:")
        print(merged_df.tail())
        
    except Exception as e:
        print(f"处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 