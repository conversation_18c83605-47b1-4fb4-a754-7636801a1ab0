import pandas as pd
import os

# 检查文件是否存在
excel_file = 'financial_analysis_results.xlsx'
if os.path.exists(excel_file):
    print(f"✓ 找到文件: {excel_file}")
    try:
        xl = pd.ExcelFile(excel_file)
        print(f"\n工作表数量: {len(xl.sheet_names)}")
        print("\n工作表列表:")
        for i, sheet in enumerate(xl.sheet_names):
            print(f"  {i+1}. {sheet}")
            
        # 检查每个工作表的数据形状
        print(f"\n各工作表数据形状:")
        for sheet in xl.sheet_names:
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet, index_col=0)
                print(f"  {sheet}: {df.shape}")
            except Exception as e:
                print(f"  {sheet}: 读取错误 - {e}")
                
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")
else:
    print(f"❌ 文件不存在: {excel_file}")
    print("\n当前目录的Excel文件:")
    for file in os.listdir('.'):
        if file.endswith('.xlsx'):
            print(f"  - {file}") 