#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Excel文件中的图表
"""

import pandas as pd
from openpyxl import load_workbook
import os

def verify_excel_charts(file_path):
    """验证Excel文件中的图表"""
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        print(f"正在验证文件: {file_path}")
        print("=" * 60)
        
        # 加载工作簿
        wb = load_workbook(file_path)
        
        # 显示所有工作表
        print("工作表列表:")
        for i, sheet_name in enumerate(wb.sheetnames, 1):
            print(f"  {i}. {sheet_name}")
        
        # 检查图表工作表
        if '图表' in wb.sheetnames:
            chart_ws = wb['图表']
            print(f"\n图表工作表详细信息:")
            print(f"  - 图表数量: {len(chart_ws._charts)}")
            
            if len(chart_ws._charts) > 0:
                print("  - 图表详情:")
                for i, chart in enumerate(chart_ws._charts, 1):
                    chart_type = type(chart).__name__
                    print(f"    图表 {i}: {chart_type}")
                    
                    # 尝试获取图表标题
                    if hasattr(chart, 'title') and chart.title:
                        try:
                            if hasattr(chart.title, 'tx') and chart.title.tx:
                                if hasattr(chart.title.tx, 'rich') and chart.title.tx.rich:
                                    title_text = ''
                                    for p in chart.title.tx.rich.p:
                                        for r in p.r:
                                            if hasattr(r, 't'):
                                                title_text += r.t
                                    if title_text:
                                        print(f"      标题: {title_text}")
                        except Exception as e:
                            print(f"      标题获取失败: {e}")
                    
                    # 检查图表位置
                    if hasattr(chart, 'anchor'):
                        print(f"      位置: {chart.anchor}")
                
                # 检查文本标注
                print("\n  - 文本标注:")
                for row in range(70, 80):
                    cell_value = chart_ws[f'A{row}'].value
                    if cell_value:
                        print(f"    A{row}: {cell_value}")
                
                print("\n✅ 图表工作表验证成功！")
                return True
            else:
                print("\n❌ 图表工作表存在但没有图表")
                return False
        else:
            print("\n❌ 未找到图表工作表")
            return False
            
    except Exception as e:
        print(f"\n❌ 验证过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    # 要验证的文件列表
    files_to_verify = [
        '滚动相关性_中证800全收益_MSCI全球市场股票全收益_730天.xlsx'
    ]
    
    for file_path in files_to_verify:
        if os.path.exists(file_path):
            success = verify_excel_charts(file_path)
            print(f"\n文件 {file_path} 验证结果: {'✅ 成功' if success else '❌ 失败'}")
            print("=" * 60)
        else:
            print(f"\n文件不存在: {file_path}")
            print("=" * 60)

if __name__ == "__main__":
    main()
