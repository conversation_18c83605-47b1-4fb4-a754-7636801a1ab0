import pandas as pd
import os
from datetime import datetime

# 根据图片定义的对应关系
SHEET_NAME_MAPPING = {
    # 债券类
    'LEGATRUU Index': '巴克莱全球债券指数',
    'LUATTRUU Index': '美国国债指数', 
    'LF98TRUU Index': '美国高收益企业债',
    'I00828US Index': '巴克莱G7债券',
    'I20344US Index': '巴克莱新兴市场本币',
    
    # 股票类
    'M2WD Index': 'MSCI全球指数',
    'SPXT Index': '标普500',
    'M2WO Index': 'MSCI发达市场',
    'M2EF Index': 'MSCI新兴市场',
    'M2WO000V Index': 'MSCI全球价值',
    'M2WO000G Index': 'MSCI全球成长',
    
    # 加密货币保持原名或使用更通用的名称
    'BTC': '比特币',
    'ETH': '以太坊',
    'USDT': 'USDT',
    'XRP': 'XRP',
    'BNB': 'BNB',
    'SOL': 'Solana',
    'TRX': '波场',
    'DOG': '狗狗币',
    'ADA': 'ADA'
}

def clean_and_extract_data(file_path, sheet_name):
    """
    从Excel sheet中提取干净的时间序列数据，跳过元数据
    """
    try:
        # 读取所有数据
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # 找到"Date"行的位置
        date_row_idx = None
        for idx, row in df.iterrows():
            if any('Date' in str(cell) for cell in row if pd.notna(cell)):
                date_row_idx = idx
                break
        
        if date_row_idx is None:
            print(f"    警告: 在sheet '{sheet_name}' 中未找到Date行")
            return None
        
        # 从Date行开始重新读取数据，以Date行作为列名
        df_clean = pd.read_excel(file_path, sheet_name=sheet_name, skiprows=date_row_idx)
        
        # 删除第一行（Date行本身）
        df_clean = df_clean.iloc[1:].reset_index(drop=True)
        
        # 获取中文名称
        chinese_name = SHEET_NAME_MAPPING.get(sheet_name, sheet_name)
        
        # 重命名列名，使用中文名称作为数据列名
        if len(df_clean.columns) > 0:
            new_columns = ['Date']
            # 除了第一列Date，其他列都用中文名命名
            for i in range(1, len(df_clean.columns)):
                if len(df_clean.columns) == 2:
                    # 只有一个数据列，直接用中文名
                    new_columns.append(chinese_name)
                else:
                    # 有多个数据列，加上后缀区分
                    new_columns.append(f"{chinese_name}_{i}")
            df_clean.columns = new_columns
        
        # 转换Date列为datetime
        try:
            df_clean['Date'] = pd.to_datetime(df_clean['Date'], errors='coerce')
            # 删除Date为空的行
            df_clean = df_clean.dropna(subset=['Date'])
        except Exception as e:
            print(f"    警告: 处理日期列时出错: {e}")
            return None
        
        return df_clean, chinese_name
        
    except Exception as e:
        print(f"    错误: 处理sheet '{sheet_name}' 时出错: {e}")
        return None

def merge_all_files():
    """
    合并所有Excel文件
    """
    print("开始合并所有Excel文件...")
    print(f"当前工作目录: {os.getcwd()}")
    print("="*80)
    
    # 文件列表
    files_to_process = [
        ("加密数据.xlsx", "加密货币"),
        ("彭博数据.xlsx", "金融指数")
    ]
    
    all_dataframes = []
    name_mapping_used = {}
    
    for file_path, file_type in files_to_process:
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            continue
            
        print(f"处理文件: {file_path} ({file_type})")
        
        # 获取所有sheet名称
        try:
            xls = pd.ExcelFile(file_path)
            print(f"  找到 {len(xls.sheet_names)} 个sheet: {xls.sheet_names}")
            
            for sheet_name in xls.sheet_names:
                print(f"  处理sheet: {sheet_name}")
                result = clean_and_extract_data(file_path, sheet_name)
                
                if result is not None:
                    df_clean, chinese_name = result
                    if not df_clean.empty:
                        print(f"    提取到 {len(df_clean)} 行有效数据")
                        print(f"    原名: {sheet_name} -> 中文名: {chinese_name}")
                        all_dataframes.append(df_clean)
                        name_mapping_used[sheet_name] = chinese_name
                    else:
                        print(f"    跳过sheet: {sheet_name} (无有效数据)")
                else:
                    print(f"    跳过sheet: {sheet_name} (处理失败)")
                    
        except Exception as e:
            print(f"  错误: 处理文件 {file_path} 时出错: {e}")
            continue
        
        print()
    
    if not all_dataframes:
        print("没有找到有效数据")
        return
    
    # 横向合并所有数据（按Date列）
    print("开始横向合并所有数据...")
    merged_df = all_dataframes[0]
    
    for i, df in enumerate(all_dataframes[1:], 2):
        print(f"  合并第 {i} 个数据集...")
        merged_df = pd.merge(merged_df, df, on='Date', how='outer')
    
    # 按时间排序
    merged_df = merged_df.sort_values('Date').reset_index(drop=True)
    
    # 使用前值填充缺失值
    print("  使用前值填充缺失值...")
    merged_df = merged_df.fillna(method='ffill')
    
    # 保存结果
    output_file = "全部数据合并.xlsx"
    
    try:
        merged_df.to_excel(output_file, index=False, engine='openpyxl')
        print("="*80)
        print(f"✅ 合并完成！结果已保存到: {output_file}")
        print(f"📊 总行数: {len(merged_df)}")
        print(f"📊 总列数: {len(merged_df.columns)}")
        
        # 显示时间范围
        if len(merged_df) > 0:
            print(f"⏰ 时间范围: {merged_df['Date'].min().strftime('%Y-%m-%d')} 到 {merged_df['Date'].max().strftime('%Y-%m-%d')}")
        
        # 打印对应关系
        print("\n🔄 Sheet名称对应关系:")
        print("-" * 50)
        for original_name, chinese_name in name_mapping_used.items():
            print(f"  {original_name:<20} -> {chinese_name}")
        
        # 显示列名
        print(f"\n📋 合并后的列名: {list(merged_df.columns)}")
        
        # 显示前几行预览
        print(f"\n📖 数据预览:")
        print(merged_df.head(3).to_string())
        
    except Exception as e:
        print(f"保存文件时出错: {e}")

if __name__ == "__main__":
    merge_all_files() 