#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
有效前沿分析脚本
功能：比较两组资产的有效前沿
作者：AI Assistant
日期：2025-07-03
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from matplotlib.ticker import FuncFormatter
import warnings
warnings.filterwarnings('ignore')

# 导入专业的优化库
from scipy.optimize import minimize
from scipy import linalg

print("已导入专业优化库: scipy.optimize")

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# ==================== 配置参数 ====================
# 第一组资产（请根据实际数据列名修改）
ASSET_GROUP_1 = ["中证800全收益", "中证全债", "COMEX黄金"]
GROUP_1_NAME = "传统资产组合"

# 第二组资产（请根据实际数据列名修改）
ASSET_GROUP_2 = ["中证800全收益", "中证全债"]
GROUP_2_NAME = "数字资产组合"

# 分析参数
NUM_PORTFOLIOS = 1000  # 生成的投资组合数量
RISK_FREE_RATE = 0.03  # 无风险利率（年化）

# 数据文件路径
DATA_FILE = "正文使用/原始指数数据_2010起.xlsx"

# 输出文件路径
OUTPUT_FILE = f"有效前沿分析_{GROUP_1_NAME}_vs_{GROUP_2_NAME}.xlsx"

def load_and_preprocess_data(file_path):
    """
    读取并预处理数据（与s2_滚动相关性分析.py相同的处理方式）
    
    Parameters:
    file_path (str): 数据文件路径
    
    Returns:
    pd.DataFrame: 预处理后的数据
    """
    print("正在读取数据文件...")
    
    try:
        # 读取Excel文件
        raw_data = pd.read_excel(file_path)
        print(f"成功读取数据，原始数据形状: {raw_data.shape}")
        
        # 显示前几行数据以便检查
        print("\n原始数据前5行:")
        print(raw_data.head())
        print("\n数据列名:")
        print(raw_data.columns.tolist())
        
        # 日期列处理
        date_col = raw_data.columns[0]  # 通常是'Date'或类似名称
        print(f"\n使用日期列: {date_col}")
        
        # 转换日期格式
        raw_data[date_col] = pd.to_datetime(raw_data[date_col])
        raw_data = raw_data.set_index(date_col)
        
        # 重新索引为连续日期并向前填充
        print("正在进行日期补全处理...")
        index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D') 
        raw_data = raw_data.reindex(index_new)  
        raw_data = raw_data.fillna(method='ffill')
        
        print(f"日期补全后数据形状: {raw_data.shape}")
        print(f"数据日期范围: {raw_data.index[0]} 到 {raw_data.index[-1]}")
        
        return raw_data
        
    except Exception as e:
        print(f"读取数据时发生错误: {e}")
        return None

def check_asset_groups(data, group1, group2):
    """
    检查指定的资产组是否存在于数据中
    
    Parameters:
    data (pd.DataFrame): 数据框
    group1 (list): 第一组资产名称列表
    group2 (list): 第二组资产名称列表
    
    Returns:
    tuple: (第一组是否完整, 第二组是否完整, 可用列名列表)
    """
    available_columns = data.columns.tolist()
    
    group1_missing = [asset for asset in group1 if asset not in available_columns]
    group2_missing = [asset for asset in group2 if asset not in available_columns]
    
    if group1_missing:
        print(f"第一组缺失资产: {group1_missing}")
    if group2_missing:
        print(f"第二组缺失资产: {group2_missing}")
    
    if group1_missing or group2_missing:
        print("\n可用的列名:")
        for i, col in enumerate(available_columns):
            print(f"{i+1}. {col}")
    
    return (len(group1_missing) == 0), (len(group2_missing) == 0), available_columns

def calculate_returns(data, asset_list):
    """
    计算资产组的收益率
    
    Parameters:
    data (pd.DataFrame): 价格数据
    asset_list (list): 资产名称列表
    
    Returns:
    pd.DataFrame: 收益率数据框
    """
    print(f"正在计算收益率，资产: {asset_list}")
    
    # 提取相关资产的价格数据
    price_data = data[asset_list].copy()
    
    # 计算日收益率
    returns_data = price_data.pct_change().dropna()
    
    # 转换为年化收益率（假设252个交易日）
    returns_data = returns_data * 252
    
    print(f"收益率计算完成，有效数据点: {len(returns_data)}")
    print(f"数据期间: {returns_data.index[0].strftime('%Y-%m-%d')} 到 {returns_data.index[-1].strftime('%Y-%m-%d')}")
    
    return returns_data

def calculate_portfolio_stats(returns, weights):
    """
    计算投资组合的统计指标
    
    Parameters:
    returns (pd.DataFrame): 收益率数据
    weights (np.array): 权重数组
    
    Returns:
    tuple: (年化收益率, 年化波动率)
    """
    # 计算投资组合收益率
    portfolio_return = np.sum(returns.mean() * weights)
    
    # 计算投资组合波动率
    portfolio_variance = np.dot(weights.T, np.dot(returns.cov(), weights))
    portfolio_volatility = np.sqrt(portfolio_variance)
    
    return portfolio_return, portfolio_volatility

def generate_efficient_frontier_scipy(returns, num_points=100):
    """
    使用 scipy 专业优化库生成有效前沿
    基于现代投资组合理论的标准二次规划方法

    Parameters:
    returns (pd.DataFrame): 收益率数据
    num_points (int): 有效前沿上的点数

    Returns:
    tuple: (收益率数组, 波动率数组, 权重矩阵)
    """
    print(f"正在使用 scipy 专业优化库生成有效前沿，计算点数: {num_points}")

    # 计算期望收益率和协方差矩阵（年化）
    mu = returns.mean() * 252  # 年化期望收益率
    Sigma = returns.cov() * 252  # 年化协方差矩阵
    n = len(mu)  # 资产数量

    print(f"资产数量: {n}")
    print(f"期望收益率范围: {mu.min():.4f} - {mu.max():.4f}")
    print(f"协方差矩阵条件数: {np.linalg.cond(Sigma):.2e}")

    # 设置目标收益率范围
    min_ret = mu.min()
    max_ret = mu.max()
    target_returns = np.linspace(min_ret, max_ret * 0.95, num_points)

    # 存储结果
    portfolio_returns = []
    portfolio_volatilities = []
    portfolio_weights = []

    def portfolio_volatility(weights, cov_matrix):
        """计算投资组合波动率"""
        return np.sqrt(np.dot(weights.T, np.dot(cov_matrix, weights)))

    def portfolio_return(weights, mean_returns):
        """计算投资组合收益率"""
        return np.dot(weights, mean_returns)

    # 约束条件
    constraints = [
        {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}  # 权重和为1
    ]

    # 权重边界（允许做空，这是理论上的有效前沿）
    bounds = tuple((-1, 1) for _ in range(n))

    for target_ret in target_returns:
        # 添加目标收益率约束
        target_constraint = {'type': 'eq', 'fun': lambda x, target=target_ret: portfolio_return(x, mu) - target}
        cons = constraints + [target_constraint]

        # 初始权重（等权重）
        x0 = np.array([1/n] * n)

        # 目标函数：最小化波动率
        def objective(x):
            return portfolio_volatility(x, Sigma)

        try:
            # 使用SLSQP方法进行优化
            result = minimize(objective, x0, method='SLSQP', bounds=bounds, constraints=cons,
                            options={'ftol': 1e-9, 'disp': False})

            if result.success:
                weights = result.x
                ret = portfolio_return(weights, mu)
                vol = portfolio_volatility(weights, Sigma)

                # 检查结果的合理性
                if vol > 0 and not np.isnan(vol) and not np.isnan(ret):
                    portfolio_returns.append(ret)
                    portfolio_volatilities.append(vol)
                    portfolio_weights.append(weights)

        except Exception as e:
            print(f"优化目标收益率 {target_ret:.4f} 时出错: {e}")
            continue

    # 转换为numpy数组
    portfolio_returns = np.array(portfolio_returns)
    portfolio_volatilities = np.array(portfolio_volatilities)
    portfolio_weights = np.array(portfolio_weights)

    print(f"有效前沿生成完成，有效点数: {len(portfolio_returns)}")

    return portfolio_returns, portfolio_volatilities, portfolio_weights

def find_optimal_portfolios_pypfopt(returns):
    """
    使用 pypfopt 找到特殊的最优投资组合

    Parameters:
    returns (pd.DataFrame): 收益率数据

    Returns:
    dict: 包含各种最优组合的字典
    """
    print("正在计算最优投资组合...")

    # 计算期望收益率和协方差矩阵
    mu = expected_returns.mean_historical_return(returns, frequency=252)
    S = risk_models.sample_cov(returns, frequency=252)

    results = {}

    # 最小方差组合
    try:
        ef_min_vol = EfficientFrontier(mu, S)
        weights_min_vol = ef_min_vol.min_volatility()
        ret_min_vol, vol_min_vol, sharpe_min_vol = ef_min_vol.portfolio_performance(
            verbose=False, risk_free_rate=RISK_FREE_RATE)

        results['min_variance'] = {
            'return': ret_min_vol,
            'volatility': vol_min_vol,
            'weights': np.array(list(weights_min_vol.values())),
            'sharpe': sharpe_min_vol
        }
    except Exception as e:
        print(f"计算最小方差组合时出错: {e}")

    # 最大夏普比率组合
    try:
        ef_max_sharpe = EfficientFrontier(mu, S)
        weights_max_sharpe = ef_max_sharpe.max_sharpe(risk_free_rate=RISK_FREE_RATE)
        ret_max_sharpe, vol_max_sharpe, sharpe_max_sharpe = ef_max_sharpe.portfolio_performance(
            verbose=False, risk_free_rate=RISK_FREE_RATE)

        results['max_sharpe'] = {
            'return': ret_max_sharpe,
            'volatility': vol_max_sharpe,
            'weights': np.array(list(weights_max_sharpe.values())),
            'sharpe': sharpe_max_sharpe
        }
    except Exception as e:
        print(f"计算最大夏普比率组合时出错: {e}")

    # 最大收益率组合（通常是单一资产）
    try:
        max_ret_asset = mu.idxmax()
        max_ret_value = mu.max()
        max_ret_vol = np.sqrt(S.loc[max_ret_asset, max_ret_asset])

        weights_max_ret = np.zeros(len(mu))
        weights_max_ret[mu.index.get_loc(max_ret_asset)] = 1.0

        results['max_return'] = {
            'return': max_ret_value,
            'volatility': max_ret_vol,
            'weights': weights_max_ret,
            'sharpe': (max_ret_value - RISK_FREE_RATE) / max_ret_vol
        }
    except Exception as e:
        print(f"计算最大收益率组合时出错: {e}")

    return results

def find_optimal_portfolios(returns, portfolio_returns, portfolio_volatilities, portfolio_weights):
    """
    找到特殊的最优投资组合

    Returns:
    dict: 包含各种最优组合的字典
    """
    results = {}

    # 最小方差组合
    min_vol_idx = np.argmin(portfolio_volatilities)
    results['min_variance'] = {
        'return': portfolio_returns[min_vol_idx],
        'volatility': portfolio_volatilities[min_vol_idx],
        'weights': portfolio_weights[min_vol_idx],
        'sharpe': (portfolio_returns[min_vol_idx] - RISK_FREE_RATE) / portfolio_volatilities[min_vol_idx]
    }

    # 最大夏普比率组合
    sharpe_ratios = (portfolio_returns - RISK_FREE_RATE) / portfolio_volatilities
    max_sharpe_idx = np.argmax(sharpe_ratios)
    results['max_sharpe'] = {
        'return': portfolio_returns[max_sharpe_idx],
        'volatility': portfolio_volatilities[max_sharpe_idx],
        'weights': portfolio_weights[max_sharpe_idx],
        'sharpe': sharpe_ratios[max_sharpe_idx]
    }

    # 最大收益率组合
    max_return_idx = np.argmax(portfolio_returns)
    results['max_return'] = {
        'return': portfolio_returns[max_return_idx],
        'volatility': portfolio_volatilities[max_return_idx],
        'weights': portfolio_weights[max_return_idx],
        'sharpe': (portfolio_returns[max_return_idx] - RISK_FREE_RATE) / portfolio_volatilities[max_return_idx]
    }

    return results

def plot_efficient_frontiers(returns1, returns2, group1_name, group2_name, asset_group1, asset_group2):
    """
    绘制两组资产的有效前沿对比图（使用专业方法）

    Parameters:
    returns1 (pd.DataFrame): 第一组资产收益率
    returns2 (pd.DataFrame): 第二组资产收益率
    group1_name (str): 第一组名称
    group2_name (str): 第二组名称
    asset_group1 (list): 第一组资产列表
    asset_group2 (list): 第二组资产列表
    """
    print("正在生成有效前沿对比图（专业方法）...")

    # 生成两组有效前沿
    ret1, vol1, weights1, ef1 = generate_efficient_frontier_pypfopt(returns1)
    ret2, vol2, weights2, ef2 = generate_efficient_frontier_pypfopt(returns2)

    # 找到最优投资组合
    optimal1 = find_optimal_portfolios_pypfopt(returns1)
    optimal2 = find_optimal_portfolios_pypfopt(returns2)

    # 创建图形
    plt.figure(figsize=(14, 10))

    # 绘制有效前沿
    plt.plot(vol1*100, ret1*100, 'b-', linewidth=3, label=f'{group1_name}有效前沿', alpha=0.8)
    plt.plot(vol2*100, ret2*100, 'r-', linewidth=3, label=f'{group2_name}有效前沿', alpha=0.8)

    # 绘制单个资产点
    for asset in asset_group1:
        if asset in returns1.columns:
            asset_ret = returns1[asset].mean() * 100
            asset_vol = returns1[asset].std() * 100
            plt.scatter(asset_vol, asset_ret, color='blue', s=80, alpha=0.7, edgecolors='darkblue')
            plt.annotate(asset, (asset_vol, asset_ret), xytext=(5, 5),
                        textcoords='offset points', fontsize=9, alpha=0.8)

    for asset in asset_group2:
        if asset in returns2.columns:
            asset_ret = returns2[asset].mean() * 100
            asset_vol = returns2[asset].std() * 100
            plt.scatter(asset_vol, asset_ret, color='red', s=80, alpha=0.7, edgecolors='darkred')
            plt.annotate(asset, (asset_vol, asset_ret), xytext=(5, 5),
                        textcoords='offset points', fontsize=9, alpha=0.8)

    # 绘制特殊投资组合点
    # 最小方差组合
    plt.scatter(optimal1['min_variance']['volatility']*100, optimal1['min_variance']['return']*100,
               color='blue', marker='s', s=150, label=f'{group1_name}最小方差组合', edgecolors='black')
    plt.scatter(optimal2['min_variance']['volatility']*100, optimal2['min_variance']['return']*100,
               color='red', marker='s', s=150, label=f'{group2_name}最小方差组合', edgecolors='black')

    # 最大夏普比率组合
    plt.scatter(optimal1['max_sharpe']['volatility']*100, optimal1['max_sharpe']['return']*100,
               color='blue', marker='*', s=200, label=f'{group1_name}最优夏普组合', edgecolors='black')
    plt.scatter(optimal2['max_sharpe']['volatility']*100, optimal2['max_sharpe']['return']*100,
               color='red', marker='*', s=200, label=f'{group2_name}最优夏普组合', edgecolors='black')

    # 绘制资本配置线（从无风险资产到切点组合）
    if RISK_FREE_RATE > 0:
        # 第一组的资本配置线
        max_vol1 = np.max(vol1) * 100
        cal_vol1 = np.linspace(0, max_vol1, 100)
        cal_ret1 = RISK_FREE_RATE * 100 + (optimal1['max_sharpe']['return']*100 - RISK_FREE_RATE*100) / (optimal1['max_sharpe']['volatility']*100) * cal_vol1
        plt.plot(cal_vol1, cal_ret1, 'b--', alpha=0.5, label=f'{group1_name}资本配置线')

        # 第二组的资本配置线
        max_vol2 = np.max(vol2) * 100
        cal_vol2 = np.linspace(0, max_vol2, 100)
        cal_ret2 = RISK_FREE_RATE * 100 + (optimal2['max_sharpe']['return']*100 - RISK_FREE_RATE*100) / (optimal2['max_sharpe']['volatility']*100) * cal_vol2
        plt.plot(cal_vol2, cal_ret2, 'r--', alpha=0.5, label=f'{group2_name}资本配置线')

        # 标记无风险利率
        plt.axhline(y=RISK_FREE_RATE*100, color='gray', linestyle=':', alpha=0.7, label=f'无风险利率 ({RISK_FREE_RATE*100:.1f}%)')

    # 设置图形属性
    plt.xlabel('年化波动率 (%)', fontsize=12)
    plt.ylabel('年化收益率 (%)', fontsize=12)
    plt.title('有效前沿对比分析（基于现代投资组合理论）', fontsize=14, fontweight='bold')
    plt.legend(fontsize=9, loc='upper left')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图片
    plt.savefig(f'有效前沿对比_{group1_name}_vs_{group2_name}.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("有效前沿对比图已生成并保存")

    # 打印最优组合信息
    print(f"\n{group1_name}最优组合信息:")
    print(f"  最小方差组合: 收益率={optimal1['min_variance']['return']*100:.2f}%, 波动率={optimal1['min_variance']['volatility']*100:.2f}%, 夏普比率={optimal1['min_variance']['sharpe']:.4f}")
    print(f"  最大夏普组合: 收益率={optimal1['max_sharpe']['return']*100:.2f}%, 波动率={optimal1['max_sharpe']['volatility']*100:.2f}%, 夏普比率={optimal1['max_sharpe']['sharpe']:.4f}")

    print(f"\n{group2_name}最优组合信息:")
    print(f"  最小方差组合: 收益率={optimal2['min_variance']['return']*100:.2f}%, 波动率={optimal2['min_variance']['volatility']*100:.2f}%, 夏普比率={optimal2['min_variance']['sharpe']:.4f}")
    print(f"  最大夏普组合: 收益率={optimal2['max_sharpe']['return']*100:.2f}%, 波动率={optimal2['max_sharpe']['volatility']*100:.2f}%, 夏普比率={optimal2['max_sharpe']['sharpe']:.4f}")

    return (ret1, vol1, weights1, optimal1), (ret2, vol2, weights2, optimal2)

def save_results_to_excel(returns1, returns2, frontier1, frontier2, group1_name, group2_name,
                         asset_group1, asset_group2, output_file):
    """
    将分析结果保存到Excel文件

    Parameters:
    returns1, returns2: 收益率数据
    frontier1, frontier2: 有效前沿数据 (收益率, 波动率, 权重, 最优组合)
    group1_name, group2_name: 组合名称
    asset_group1, asset_group2: 资产列表
    output_file: 输出文件路径
    """
    print(f"正在保存结果到文件: {output_file}")

    try:
        # 解包数据
        if len(frontier1) == 4:
            ret1, vol1, weights1, optimal1 = frontier1
        else:
            ret1, vol1, weights1 = frontier1
            optimal1 = None

        if len(frontier2) == 4:
            ret2, vol2, weights2, optimal2 = frontier2
        else:
            ret2, vol2, weights2 = frontier2
            optimal2 = None

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 保存第一组有效前沿数据
            if len(ret1) > 0:
                frontier1_df = pd.DataFrame({
                    '年化收益率(%)': ret1 * 100,
                    '年化波动率(%)': vol1 * 100,
                    '夏普比率': (ret1 - RISK_FREE_RATE) / vol1
                })
                # 添加权重列
                for i, asset in enumerate(asset_group1):
                    if i < weights1.shape[1]:
                        frontier1_df[f'{asset}_权重(%)'] = weights1[:, i] * 100

                frontier1_df.to_excel(writer, sheet_name=f'{group1_name}_有效前沿', index=False)

            # 保存第二组有效前沿数据
            if len(ret2) > 0:
                frontier2_df = pd.DataFrame({
                    '年化收益率(%)': ret2 * 100,
                    '年化波动率(%)': vol2 * 100,
                    '夏普比率': (ret2 - RISK_FREE_RATE) / vol2
                })
                # 添加权重列
                for i, asset in enumerate(asset_group2):
                    if i < weights2.shape[1]:
                        frontier2_df[f'{asset}_权重(%)'] = weights2[:, i] * 100

                frontier2_df.to_excel(writer, sheet_name=f'{group2_name}_有效前沿', index=False)

            # 保存最优组合信息
            if optimal1 and optimal2:
                optimal_data = []

                # 第一组最优组合
                for portfolio_type, portfolio_data in optimal1.items():
                    row = {
                        '资产组': group1_name,
                        '组合类型': portfolio_type,
                        '年化收益率(%)': portfolio_data['return'] * 100,
                        '年化波动率(%)': portfolio_data['volatility'] * 100,
                        '夏普比率': portfolio_data['sharpe']
                    }
                    # 添加权重信息
                    for i, asset in enumerate(asset_group1):
                        if i < len(portfolio_data['weights']):
                            row[f'{asset}_权重(%)'] = portfolio_data['weights'][i] * 100
                    optimal_data.append(row)

                # 第二组最优组合
                for portfolio_type, portfolio_data in optimal2.items():
                    row = {
                        '资产组': group2_name,
                        '组合类型': portfolio_type,
                        '年化收益率(%)': portfolio_data['return'] * 100,
                        '年化波动率(%)': portfolio_data['volatility'] * 100,
                        '夏普比率': portfolio_data['sharpe']
                    }
                    # 添加权重信息
                    for i, asset in enumerate(asset_group2):
                        if i < len(portfolio_data['weights']):
                            row[f'{asset}_权重(%)'] = portfolio_data['weights'][i] * 100
                    optimal_data.append(row)

                optimal_df = pd.DataFrame(optimal_data)
                optimal_df.to_excel(writer, sheet_name='最优组合', index=False)

            # 保存资产统计信息
            stats_data = []

            # 第一组资产统计
            for asset in asset_group1:
                if asset in returns1.columns:
                    stats_data.append({
                        '资产组': group1_name,
                        '资产名称': asset,
                        '年化收益率': returns1[asset].mean(),
                        '年化波动率': returns1[asset].std(),
                        '夏普比率': (returns1[asset].mean() - RISK_FREE_RATE) / returns1[asset].std()
                    })

            # 第二组资产统计
            for asset in asset_group2:
                if asset in returns2.columns:
                    stats_data.append({
                        '资产组': group2_name,
                        '资产名称': asset,
                        '年化收益率': returns2[asset].mean(),
                        '年化波动率': returns2[asset].std(),
                        '夏普比率': (returns2[asset].mean() - RISK_FREE_RATE) / returns2[asset].std()
                    })

            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='资产统计', index=False)

            # 保存参数说明
            params_info = pd.DataFrame({
                '参数': ['第一组资产', '第二组资产', '分析日期', '数据来源', '数据期间'],
                '值': [
                    ', '.join(asset_group1),
                    ', '.join(asset_group2),
                    pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
                    DATA_FILE,
                    f"{returns1.index[0].strftime('%Y-%m-%d')} 至 {returns1.index[-1].strftime('%Y-%m-%d')}"
                ]
            })
            params_info.to_excel(writer, sheet_name='参数说明', index=False)

        print(f"结果已成功保存到: {output_file}")

    except Exception as e:
        print(f"保存文件时发生错误: {e}")

def print_analysis_summary(frontier1, frontier2, group1_name, group2_name):
    """
    打印分析结果摘要
    """
    # 解包数据
    if len(frontier1) == 4:
        ret1, vol1, weights1, optimal1 = frontier1
    else:
        ret1, vol1, weights1 = frontier1
        optimal1 = None

    if len(frontier2) == 4:
        ret2, vol2, weights2, optimal2 = frontier2
    else:
        ret2, vol2, weights2 = frontier2
        optimal2 = None

    print("\n" + "=" * 80)
    print("有效前沿分析结果摘要（基于现代投资组合理论）")
    print("=" * 80)

    if len(ret1) > 0:
        print(f"\n{group1_name}:")
        print(f"  有效前沿点数: {len(ret1)}")
        print(f"  收益率范围: {ret1.min()*100:.2f}% - {ret1.max()*100:.2f}%")
        print(f"  波动率范围: {vol1.min()*100:.2f}% - {vol1.max()*100:.2f}%")

        if optimal1:
            print(f"  最小方差组合:")
            print(f"    收益率: {optimal1['min_variance']['return']*100:.2f}%")
            print(f"    波动率: {optimal1['min_variance']['volatility']*100:.2f}%")
            print(f"    夏普比率: {optimal1['min_variance']['sharpe']:.4f}")
            print(f"  最大夏普比率组合:")
            print(f"    收益率: {optimal1['max_sharpe']['return']*100:.2f}%")
            print(f"    波动率: {optimal1['max_sharpe']['volatility']*100:.2f}%")
            print(f"    夏普比率: {optimal1['max_sharpe']['sharpe']:.4f}")

    if len(ret2) > 0:
        print(f"\n{group2_name}:")
        print(f"  有效前沿点数: {len(ret2)}")
        print(f"  收益率范围: {ret2.min()*100:.2f}% - {ret2.max()*100:.2f}%")
        print(f"  波动率范围: {vol2.min()*100:.2f}% - {vol2.max()*100:.2f}%")

        if optimal2:
            print(f"  最小方差组合:")
            print(f"    收益率: {optimal2['min_variance']['return']*100:.2f}%")
            print(f"    波动率: {optimal2['min_variance']['volatility']*100:.2f}%")
            print(f"    夏普比率: {optimal2['min_variance']['sharpe']:.4f}")
            print(f"  最大夏普比率组合:")
            print(f"    收益率: {optimal2['max_sharpe']['return']*100:.2f}%")
            print(f"    波动率: {optimal2['max_sharpe']['volatility']*100:.2f}%")
            print(f"    夏普比率: {optimal2['max_sharpe']['sharpe']:.4f}")

    print("\n" + "=" * 80)

def main():
    """
    主函数：执行完整的有效前沿分析流程
    """
    print("=" * 80)
    print("有效前沿分析脚本")
    print("=" * 80)
    print(f"第一组资产 ({GROUP_1_NAME}): {ASSET_GROUP_1}")
    print(f"第二组资产 ({GROUP_2_NAME}): {ASSET_GROUP_2}")
    print("=" * 80)

    # 1. 读取和预处理数据
    data = load_and_preprocess_data(DATA_FILE)
    if data is None:
        print("数据读取失败，程序终止")
        return

    # 2. 检查资产组是否存在
    group1_complete, group2_complete, available_columns = check_asset_groups(
        data, ASSET_GROUP_1, ASSET_GROUP_2)

    if not (group1_complete and group2_complete):
        print("指定的资产组不完整，请检查配置参数")
        return

    # 3. 计算收益率
    returns1 = calculate_returns(data, ASSET_GROUP_1)
    returns2 = calculate_returns(data, ASSET_GROUP_2)

    # 确保两组数据的时间范围一致
    common_dates = returns1.index.intersection(returns2.index)
    returns1 = returns1.loc[common_dates]
    returns2 = returns2.loc[common_dates]

    print(f"\n共同数据期间: {len(common_dates)}天")
    print(f"分析期间: {common_dates[0].strftime('%Y-%m-%d')} 到 {common_dates[-1].strftime('%Y-%m-%d')}")

    # 4. 生成有效前沿对比图
    frontier1, frontier2 = plot_efficient_frontiers(
        returns1, returns2, GROUP_1_NAME, GROUP_2_NAME, ASSET_GROUP_1, ASSET_GROUP_2)

    # 5. 保存结果到Excel
    save_results_to_excel(returns1, returns2, frontier1, frontier2,
                         GROUP_1_NAME, GROUP_2_NAME, ASSET_GROUP_1, ASSET_GROUP_2, OUTPUT_FILE)

    # 6. 打印分析摘要
    print_analysis_summary(frontier1, frontier2, GROUP_1_NAME, GROUP_2_NAME)

    print(f"\n分析完成！结果文件: {OUTPUT_FILE}")
    print("图片文件: 有效前沿对比_{}_vs_{}.png".format(GROUP_1_NAME, GROUP_2_NAME))

if __name__ == "__main__":
    main()
