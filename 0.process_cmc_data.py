# -*- coding: utf-8 -*-
"""
CoinMarketCap数据处理脚本
从coinmarketcap文件夹中提取所有数据并转换为CSV格式，输出到output文件夹
"""

import pandas as pd
import json
import os
from datetime import datetime

def process_dominance_data():
    """处理dominance数据"""
    print("处理dominance数据...")
    
    try:
        with open("coinmarketcap/dominance.txt", "r", encoding="utf-8") as f:
            content = f.read().strip()
        
        data = json.loads(content)
        configs = data["data"]["configs"]
        config_names = [config["name"] for config in configs]
        points = data["data"]["points"]
        
        processed_data = []
        for point in points:
            timestamp = int(point["timestamp"])
            date = datetime.fromtimestamp(timestamp)
            dominance_values = point["dominance"]
            
            row = {
                "timestamp": timestamp,
                "date": date.strftime("%Y-%m-%d"),
                "datetime": date.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            for i, value in enumerate(dominance_values):
                if i < len(config_names):
                    row[f"{config_names[i]}_dominance"] = value
                else:
                    row[f"dominance_{i}"] = value
            
            processed_data.append(row)
        
        df = pd.DataFrame(processed_data)
        df.to_csv("output/dominance_extracted.csv", index=False, encoding="utf-8")
        
        print(f"  成功处理dominance数据: {len(df)}行")
        return df
        
    except Exception as e:
        print(f"  处理dominance数据失败: {e}")
        return None

def main():
    """主函数"""
    print("开始处理CoinMarketCap数据")
    print("="*60)
    
    # 确保output目录存在
    os.makedirs("output", exist_ok=True)
    
    # 处理dominance数据
    dominance_df = process_dominance_data()
    
    # 复制其他CSV文件
    csv_files = ["historical_extracted.csv", "tracked1_extracted.csv", "tracked2_extracted.csv"]
    
    for csv_file in csv_files:
        source_path = f"coinmarketcap/{csv_file}"
        if os.path.exists(source_path):
            try:
                df = pd.read_csv(source_path)
                df.to_csv(f"output/{csv_file}", index=False, encoding="utf-8")
                print(f"复制 {csv_file}: {len(df)}行")
            except Exception as e:
                print(f"复制 {csv_file} 失败: {e}")
        else:
            print(f"文件不存在: {source_path}")
    
    print("\n" + "="*60)
    print("处理完成！")
    print(f"输入目录: coinmarketcap/")
    print(f"输出目录: output/")
    
    # 列出生成的文件
    if os.path.exists("output"):
        print("生成的文件:")
        for file in os.listdir("output"):
            file_path = os.path.join("output", file)
            file_size = os.path.getsize(file_path) / 1024  # KB
            print(f"  - {file} ({file_size:.1f} KB)")
    
    print("="*60)

if __name__ == "__main__":
    main() 