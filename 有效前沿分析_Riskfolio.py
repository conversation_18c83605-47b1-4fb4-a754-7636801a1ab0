#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
有效前沿分析脚本 - 使用 Riskfolio-Lib 专业库
基于现代投资组合理论进行有效前沿优化分析

作者: AI Assistant
日期: 2025-01-03
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
from datetime import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
warnings.filterwarnings('ignore')

# 导入 Riskfolio-Lib 专业投资组合优化库
try:
    import riskfolio as rp
    print("已成功导入 Riskfolio-Lib 专业投资组合优化库")
except ImportError:
    print("错误：未找到 Riskfolio-Lib 库，请先安装：pip install riskfolio-lib")
    exit(1)

# 配置参数
DATA_FILE = "正文使用/原始指数数据_2010起.xlsx"
RISK_FREE_RATE = 0.02  # 无风险利率 2%

# 定义两个资产组合进行对比分析
ASSET_GROUP_1 = ['中证800全收益', '中证全债', 'COMEX黄金']  # 传统资产组合
ASSET_GROUP_2 = ['Bitcoin', 'Ethereum', '中证800全收益']    # 数字资产组合

GROUP1_NAME = "传统资产组合"
GROUP2_NAME = "数字资产组合"

def load_and_preprocess_data(file_path):
    """
    加载并预处理数据
    
    Parameters:
    file_path (str): 数据文件路径
    
    Returns:
    pd.DataFrame: 预处理后的数据
    """
    print("正在读取数据文件...")
    
    # 读取Excel文件
    raw_data = pd.read_excel(file_path)
    print(f"成功读取数据，原始数据形状: {raw_data.shape}")
    
    # 显示数据基本信息
    print(f"\n原始数据前5行:")
    print(raw_data.head())
    print(f"\n数据列名:")
    print(raw_data.columns.tolist())
    
    # 使用提供的日期补全处理代码
    date_col = raw_data.columns[0]  # 通常是'Date'
    print(f"\n使用日期列: {date_col}")
    
    print("正在进行日期补全处理...")
    raw_data[date_col] = pd.to_datetime(raw_data[date_col])
    raw_data = raw_data.set_index(date_col)
    
    # 重新索引为连续日期并向前填充
    index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D') 
    raw_data = raw_data.reindex(index_new)  
    raw_data = raw_data.fillna(method='ffill')
    
    print(f"日期补全后数据形状: {raw_data.shape}")
    print(f"数据日期范围: {raw_data.index[0]} 到 {raw_data.index[-1]}")
    
    return raw_data

def check_asset_availability(data, group1, group2):
    """
    检查资产数据的可用性
    
    Parameters:
    data (pd.DataFrame): 数据框
    group1 (list): 第一组资产列表
    group2 (list): 第二组资产列表
    
    Returns:
    tuple: (group1_available, group2_available, available_columns)
    """
    available_columns = data.columns.tolist()
    
    # 检查第一组资产
    group1_missing = [asset for asset in group1 if asset not in available_columns]
    group2_missing = [asset for asset in group2 if asset not in available_columns]
    
    if group1_missing:
        print(f"\n警告：第一组资产中缺少以下数据列: {group1_missing}")
    if group2_missing:
        print(f"\n警告：第二组资产中缺少以下数据列: {group2_missing}")
    
    if group1_missing or group2_missing:
        print(f"\n可用的数据列:")
        for i, col in enumerate(available_columns):
            print(f"{i+1}. {col}")
    
    return (len(group1_missing) == 0), (len(group2_missing) == 0), available_columns

def calculate_returns(data, asset_list):
    """
    计算资产组的收益率
    
    Parameters:
    data (pd.DataFrame): 价格数据
    asset_list (list): 资产名称列表
    
    Returns:
    pd.DataFrame: 收益率数据框
    """
    print(f"正在计算收益率，资产: {asset_list}")
    
    # 提取相关资产的价格数据
    price_data = data[asset_list].copy()
    
    # 计算日收益率
    returns_data = price_data.pct_change().dropna()
    
    print(f"收益率计算完成，有效数据点: {len(returns_data)}")
    print(f"数据期间: {returns_data.index[0].strftime('%Y-%m-%d')} 到 {returns_data.index[-1].strftime('%Y-%m-%d')}")
    
    return returns_data

def generate_efficient_frontier_riskfolio(returns, num_points=100):
    """
    使用 Riskfolio-Lib 专业库生成有效前沿
    
    Parameters:
    returns (pd.DataFrame): 收益率数据
    num_points (int): 有效前沿上的点数
    
    Returns:
    tuple: (收益率数组, 波动率数组, 权重矩阵, 最优组合字典)
    """
    print(f"正在使用 Riskfolio-Lib 专业库生成有效前沿，计算点数: {num_points}")
    
    # 清理数据：删除包含 NaN 或无穷大的行
    returns_clean = returns.dropna()
    returns_clean = returns_clean.replace([np.inf, -np.inf], np.nan).dropna()
    print(f"数据清理后，有效数据点: {len(returns_clean)}")
    
    # 检查数据质量
    print(f"数据统计:")
    for col in returns_clean.columns:
        print(f"  {col}: 均值={returns_clean[col].mean():.6f}, 标准差={returns_clean[col].std():.6f}")
    
    if len(returns_clean) < 252:  # 至少需要一年的数据
        print("警告：数据点不足，可能影响分析质量")
    
    # 计算期望收益率和协方差矩阵
    mu = returns_clean.mean() * 252  # 年化期望收益率
    cov = returns_clean.cov() * 252  # 年化协方差矩阵

    print(f"资产数量: {len(mu)}")
    print(f"期望收益率: {dict(mu)}")
    print(f"协方差矩阵形状: {cov.shape}")

    # 检查是否有 NaN 值
    if pd.isna(mu).any() or pd.isna(cov).any().any():
        print("错误：期望收益率或协方差矩阵包含 NaN 值")
        return np.array([]), np.array([]), np.array([]), {}

    # 创建 Portfolio 对象
    port = rp.Portfolio(returns=returns_clean)
    port.mu = mu
    port.cov = cov

    # 设置目标收益率范围
    min_ret = mu.min()
    max_ret = mu.max()
    target_returns = np.linspace(min_ret, max_ret * 0.95, num_points)

    # 存储结果
    portfolio_returns = []
    portfolio_volatilities = []
    portfolio_weights = []

    for target_ret in target_returns:
        try:
            # 设置目标收益率约束
            port.lowerret = target_ret

            # 优化：给定目标收益率，最小化风险
            w = port.optimization(model='Classic', rm='MV', obj='MinRisk', rf=0, l=0, hist=True)

            if w is not None and not w.isna().any().any():
                # 计算组合性能
                ret = (w.T @ mu).iloc[0, 0]  # 年化收益率
                vol = np.sqrt(w.T @ cov @ w).iloc[0, 0]  # 年化波动率

                portfolio_returns.append(ret)
                portfolio_volatilities.append(vol)
                portfolio_weights.append(w.values.flatten())

        except Exception as e:
            # 如果优化失败，跳过这个点
            continue
    
    # 转换为numpy数组
    portfolio_returns = np.array(portfolio_returns)
    portfolio_volatilities = np.array(portfolio_volatilities)
    portfolio_weights = np.array(portfolio_weights)
    
    print(f"有效前沿生成完成，有效点数: {len(portfolio_returns)}")
    
    # 计算特殊的最优组合
    optimal_portfolios = find_optimal_portfolios_riskfolio(returns_clean)
    
    return portfolio_returns, portfolio_volatilities, portfolio_weights, optimal_portfolios

def find_optimal_portfolios_riskfolio(returns):
    """
    使用 Riskfolio-Lib 找到特殊的最优投资组合
    
    Parameters:
    returns (pd.DataFrame): 收益率数据
    
    Returns:
    dict: 包含各种最优组合的字典
    """
    print("正在计算最优投资组合...")
    
    # 清理数据
    returns_clean = returns.dropna()
    returns_clean = returns_clean.replace([np.inf, -np.inf], np.nan).dropna()

    # 计算期望收益率和协方差矩阵
    mu = returns_clean.mean() * 252  # 年化期望收益率
    cov = returns_clean.cov() * 252  # 年化协方差矩阵

    # 创建 Portfolio 对象
    port = rp.Portfolio(returns=returns_clean)
    port.mu = mu
    port.cov = cov

    results = {}

    # 最小方差组合
    try:
        w_min_vol = port.optimization(model='Classic', rm='MV', obj='MinRisk', rf=0, l=0, hist=True)
        if w_min_vol is not None:
            ret_min_vol = (w_min_vol.T @ mu).iloc[0, 0]
            vol_min_vol = np.sqrt(w_min_vol.T @ cov @ w_min_vol).iloc[0, 0]
            sharpe_min_vol = (ret_min_vol - RISK_FREE_RATE) / vol_min_vol

            results['min_variance'] = {
                'return': ret_min_vol,
                'volatility': vol_min_vol,
                'weights': w_min_vol.values.flatten(),
                'sharpe': sharpe_min_vol
            }
    except Exception as e:
        print(f"计算最小方差组合时出错: {e}")

    # 最大夏普比率组合
    try:
        w_max_sharpe = port.optimization(model='Classic', rm='MV', obj='Sharpe', rf=0, l=0, hist=True)
        if w_max_sharpe is not None:
            ret_max_sharpe = (w_max_sharpe.T @ mu).iloc[0, 0]
            vol_max_sharpe = np.sqrt(w_max_sharpe.T @ cov @ w_max_sharpe).iloc[0, 0]
            sharpe_max_sharpe = (ret_max_sharpe - RISK_FREE_RATE) / vol_max_sharpe

            results['max_sharpe'] = {
                'return': ret_max_sharpe,
                'volatility': vol_max_sharpe,
                'weights': w_max_sharpe.values.flatten(),
                'sharpe': sharpe_max_sharpe
            }
    except Exception as e:
        print(f"计算最大夏普比率组合时出错: {e}")
    
    return results

def plot_efficient_frontiers(returns1, returns2, group1_name, group2_name):
    """
    绘制两组资产的有效前沿对比图

    Parameters:
    returns1 (pd.DataFrame): 第一组资产收益率
    returns2 (pd.DataFrame): 第二组资产收益率
    group1_name (str): 第一组名称
    group2_name (str): 第二组名称

    Returns:
    tuple: (frontier1_data, frontier2_data)
    """
    print("正在生成有效前沿对比图（Riskfolio-Lib 专业方法）...")

    # 生成两组有效前沿
    ret1, vol1, weights1, optimal1 = generate_efficient_frontier_riskfolio(returns1)
    ret2, vol2, weights2, optimal2 = generate_efficient_frontier_riskfolio(returns2)

    # 创建图形
    plt.figure(figsize=(12, 8))

    # 绘制有效前沿
    if len(ret1) > 0:
        plt.plot(vol1*100, ret1*100, 'b-', linewidth=2, label=f'{group1_name}有效前沿', alpha=0.8)
    if len(ret2) > 0:
        plt.plot(vol2*100, ret2*100, 'r-', linewidth=2, label=f'{group2_name}有效前沿', alpha=0.8)

    # 绘制特殊投资组合点（如果计算成功）
    # 最小方差组合
    if 'min_variance' in optimal1:
        plt.scatter(optimal1['min_variance']['volatility']*100, optimal1['min_variance']['return']*100,
                   color='blue', marker='s', s=150, label=f'{group1_name}最小方差组合', edgecolors='black')
    if 'min_variance' in optimal2:
        plt.scatter(optimal2['min_variance']['volatility']*100, optimal2['min_variance']['return']*100,
                   color='red', marker='s', s=150, label=f'{group2_name}最小方差组合', edgecolors='black')

    # 最大夏普比率组合
    if 'max_sharpe' in optimal1:
        plt.scatter(optimal1['max_sharpe']['volatility']*100, optimal1['max_sharpe']['return']*100,
                   color='blue', marker='*', s=200, label=f'{group1_name}最优夏普组合', edgecolors='black')
    if 'max_sharpe' in optimal2:
        plt.scatter(optimal2['max_sharpe']['volatility']*100, optimal2['max_sharpe']['return']*100,
                   color='red', marker='*', s=200, label=f'{group2_name}最优夏普组合', edgecolors='black')

    # 设置图形属性
    plt.xlabel('年化波动率 (%)', fontsize=12)
    plt.ylabel('年化收益率 (%)', fontsize=12)
    plt.title('有效前沿对比分析\n基于 Riskfolio-Lib 现代投资组合理论', fontsize=14, fontweight='bold')
    plt.legend(loc='upper left', fontsize=10)
    plt.grid(True, alpha=0.3)

    # 设置坐标轴范围
    if len(ret1) > 0 or len(ret2) > 0:
        all_vols = np.concatenate([vol1*100, vol2*100]) if len(ret1) > 0 and len(ret2) > 0 else (vol1*100 if len(ret1) > 0 else vol2*100)
        all_rets = np.concatenate([ret1*100, ret2*100]) if len(ret1) > 0 and len(ret2) > 0 else (ret1*100 if len(ret1) > 0 else ret2*100)

        if len(all_vols) > 0 and len(all_rets) > 0:
            max_vol = np.max(all_vols)
            max_ret = np.max(all_rets)
            min_ret = np.min(all_rets)

            plt.xlim(0, max_vol * 1.1)
            plt.ylim(min_ret * 0.9, max_ret * 1.1)

    # 保存图片
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_filename = f"有效前沿对比_{group1_name}_vs_{group2_name}_{timestamp}.png"
    plt.tight_layout()
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"有效前沿对比图已生成并保存: {plot_filename}")

    # 显示最优组合信息
    if optimal1:
        print(f"\n{group1_name}最优组合信息:")
        for key, value in optimal1.items():
            if key == 'min_variance':
                print(f"  最小方差组合: 收益率={value['return']:.2%}, 波动率={value['volatility']:.2%}, 夏普比率={value['sharpe']:.4f}")
            elif key == 'max_sharpe':
                print(f"  最大夏普组合: 收益率={value['return']:.2%}, 波动率={value['volatility']:.2%}, 夏普比率={value['sharpe']:.4f}")

    if optimal2:
        print(f"\n{group2_name}最优组合信息:")
        for key, value in optimal2.items():
            if key == 'min_variance':
                print(f"  最小方差组合: 收益率={value['return']:.2%}, 波动率={value['volatility']:.2%}, 夏普比率={value['sharpe']:.4f}")
            elif key == 'max_sharpe':
                print(f"  最大夏普组合: 收益率={value['return']:.2%}, 波动率={value['volatility']:.2%}, 夏普比率={value['sharpe']:.4f}")

    return (ret1, vol1, weights1, optimal1), (ret2, vol2, weights2, optimal2)

def save_results_to_excel(frontier1, frontier2, group1_name, group2_name, asset_group1, asset_group2):
    """
    将结果保存到Excel文件

    Parameters:
    frontier1 (tuple): 第一组有效前沿数据
    frontier2 (tuple): 第二组有效前沿数据
    group1_name (str): 第一组名称
    group2_name (str): 第二组名称
    asset_group1 (list): 第一组资产列表
    asset_group2 (list): 第二组资产列表
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"有效前沿分析_{group1_name}_vs_{group2_name}_{timestamp}.xlsx"

    print(f"正在保存结果到文件: {output_file}")

    # 解包数据
    if len(frontier1) == 4:
        ret1, vol1, weights1, optimal1 = frontier1
    else:
        ret1, vol1, weights1 = frontier1
        optimal1 = None

    if len(frontier2) == 4:
        ret2, vol2, weights2, optimal2 = frontier2
    else:
        ret2, vol2, weights2 = frontier2
        optimal2 = None

    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 保存第一组有效前沿数据
            if len(ret1) > 0:
                frontier1_df = pd.DataFrame({
                    '年化收益率': ret1,
                    '年化波动率': vol1,
                    '夏普比率': (ret1 - RISK_FREE_RATE) / vol1
                })

                # 添加权重列
                for i, asset in enumerate(asset_group1):
                    if i < weights1.shape[1]:
                        frontier1_df[f'{asset}_权重'] = weights1[:, i]

                frontier1_df.to_excel(writer, sheet_name=f'{group1_name}_有效前沿', index=False)

            # 保存第二组有效前沿数据
            if len(ret2) > 0:
                frontier2_df = pd.DataFrame({
                    '年化收益率': ret2,
                    '年化波动率': vol2,
                    '夏普比率': (ret2 - RISK_FREE_RATE) / vol2
                })

                # 添加权重列
                for i, asset in enumerate(asset_group2):
                    if i < weights2.shape[1]:
                        frontier2_df[f'{asset}_权重'] = weights2[:, i]

                frontier2_df.to_excel(writer, sheet_name=f'{group2_name}_有效前沿', index=False)

            # 保存最优组合权重
            optimal_data = []

            if optimal1:
                for key, value in optimal1.items():
                    row = {
                        '资产组合': group1_name,
                        '组合类型': '最小方差组合' if key == 'min_variance' else '最大夏普比率组合',
                        '年化收益率': value['return'],
                        '年化波动率': value['volatility'],
                        '夏普比率': value['sharpe']
                    }

                    # 添加权重
                    for i, asset in enumerate(asset_group1):
                        if i < len(value['weights']):
                            row[f'{asset}_权重'] = value['weights'][i]

                    optimal_data.append(row)

            if optimal2:
                for key, value in optimal2.items():
                    row = {
                        '资产组合': group2_name,
                        '组合类型': '最小方差组合' if key == 'min_variance' else '最大夏普比率组合',
                        '年化收益率': value['return'],
                        '年化波动率': value['volatility'],
                        '夏普比率': value['sharpe']
                    }

                    # 添加权重
                    for i, asset in enumerate(asset_group2):
                        if i < len(value['weights']):
                            row[f'{asset}_权重'] = value['weights'][i]

                    optimal_data.append(row)

            if optimal_data:
                optimal_df = pd.DataFrame(optimal_data)
                optimal_df.to_excel(writer, sheet_name='最优组合权重', index=False)

            # 保存分析摘要
            summary_data = []

            if len(ret1) > 0:
                summary_data.append({
                    '资产组合': group1_name,
                    '资产列表': ', '.join(asset_group1),
                    '有效前沿点数': len(ret1),
                    '收益率范围': f"{ret1.min():.2%} - {ret1.max():.2%}",
                    '波动率范围': f"{vol1.min():.2%} - {vol1.max():.2%}",
                    '最大夏普比率': f"{((ret1 - RISK_FREE_RATE) / vol1).max():.4f}" if len(ret1) > 0 else "N/A"
                })

            if len(ret2) > 0:
                summary_data.append({
                    '资产组合': group2_name,
                    '资产列表': ', '.join(asset_group2),
                    '有效前沿点数': len(ret2),
                    '收益率范围': f"{ret2.min():.2%} - {ret2.max():.2%}",
                    '波动率范围': f"{vol2.min():.2%} - {vol2.max():.2%}",
                    '最大夏普比率': f"{((ret2 - RISK_FREE_RATE) / vol2).max():.4f}" if len(ret2) > 0 else "N/A"
                })

            if summary_data:
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='分析摘要', index=False)

        print(f"结果已成功保存到: {output_file}")
        return output_file

    except Exception as e:
        print(f"保存Excel文件时出错: {e}")
        return None

def print_analysis_summary(frontier1, frontier2, group1_name, group2_name, asset_group1, asset_group2):
    """
    打印分析结果摘要

    Parameters:
    frontier1 (tuple): 第一组有效前沿数据
    frontier2 (tuple): 第二组有效前沿数据
    group1_name (str): 第一组名称
    group2_name (str): 第二组名称
    asset_group1 (list): 第一组资产列表
    asset_group2 (list): 第二组资产列表
    """
    # 解包数据
    if len(frontier1) == 4:
        ret1, vol1, weights1, optimal1 = frontier1
    else:
        ret1, vol1, weights1 = frontier1
        optimal1 = None

    if len(frontier2) == 4:
        ret2, vol2, weights2, optimal2 = frontier2
    else:
        ret2, vol2, weights2 = frontier2
        optimal2 = None

    print("\n" + "=" * 80)
    print("有效前沿分析结果摘要（基于 Riskfolio-Lib 现代投资组合理论）")
    print("=" * 80)

    # 第一组摘要
    if len(ret1) > 0:
        print(f"\n{group1_name}:")
        print(f"  资产列表: {', '.join(asset_group1)}")
        print(f"  有效前沿点数: {len(ret1)}")
        print(f"  收益率范围: {ret1.min():.2%} - {ret1.max():.2%}")
        print(f"  波动率范围: {vol1.min():.2%} - {vol1.max():.2%}")

        if optimal1 and 'min_variance' in optimal1:
            mv = optimal1['min_variance']
            print(f"  最小方差组合:")
            print(f"    收益率: {mv['return']:.2%}")
            print(f"    波动率: {mv['volatility']:.2%}")
            print(f"    夏普比率: {mv['sharpe']:.4f}")

        if optimal1 and 'max_sharpe' in optimal1:
            ms = optimal1['max_sharpe']
            print(f"  最大夏普比率组合:")
            print(f"    收益率: {ms['return']:.2%}")
            print(f"    波动率: {ms['volatility']:.2%}")
            print(f"    夏普比率: {ms['sharpe']:.4f}")

    # 第二组摘要
    if len(ret2) > 0:
        print(f"\n{group2_name}:")
        print(f"  资产列表: {', '.join(asset_group2)}")
        print(f"  有效前沿点数: {len(ret2)}")
        print(f"  收益率范围: {ret2.min():.2%} - {ret2.max():.2%}")
        print(f"  波动率范围: {vol2.min():.2%} - {vol2.max():.2%}")

        if optimal2 and 'min_variance' in optimal2:
            mv = optimal2['min_variance']
            print(f"  最小方差组合:")
            print(f"    收益率: {mv['return']:.2%}")
            print(f"    波动率: {mv['volatility']:.2%}")
            print(f"    夏普比率: {mv['sharpe']:.4f}")

        if optimal2 and 'max_sharpe' in optimal2:
            ms = optimal2['max_sharpe']
            print(f"  最大夏普比率组合:")
            print(f"    收益率: {ms['return']:.2%}")
            print(f"    波动率: {ms['volatility']:.2%}")
            print(f"    夏普比率: {ms['sharpe']:.4f}")

    print("\n" + "=" * 80)

def main():
    """
    主函数
    """
    print("=" * 80)
    print("有效前沿分析脚本 - 基于 Riskfolio-Lib")
    print("=" * 80)
    print(f"第一组资产 ({GROUP1_NAME}): {ASSET_GROUP_1}")
    print(f"第二组资产 ({GROUP2_NAME}): {ASSET_GROUP_2}")
    print("=" * 80)

    # 1. 加载和预处理数据
    try:
        data = load_and_preprocess_data(DATA_FILE)
    except Exception as e:
        print(f"数据加载失败: {e}")
        return

    # 2. 检查资产可用性
    group1_ok, group2_ok, available_cols = check_asset_availability(data, ASSET_GROUP_1, ASSET_GROUP_2)

    if not group1_ok or not group2_ok:
        print("错误：部分资产数据不可用，请检查资产名称或数据文件")
        return

    # 3. 计算收益率
    returns1 = calculate_returns(data, ASSET_GROUP_1)
    returns2 = calculate_returns(data, ASSET_GROUP_2)

    # 找到共同的数据期间
    common_start = max(returns1.index[0], returns2.index[0])
    common_end = min(returns1.index[-1], returns2.index[-1])

    returns1 = returns1.loc[common_start:common_end]
    returns2 = returns2.loc[common_start:common_end]

    print(f"\n共同数据期间: {len(returns1)}天")
    print(f"分析期间: {common_start.strftime('%Y-%m-%d')} 到 {common_end.strftime('%Y-%m-%d')}")

    # 4. 生成有效前沿对比图
    try:
        frontier1, frontier2 = plot_efficient_frontiers(
            returns1, returns2, GROUP1_NAME, GROUP2_NAME
        )
    except Exception as e:
        print(f"有效前沿计算失败: {e}")
        return

    # 5. 保存结果到Excel
    try:
        excel_file = save_results_to_excel(
            frontier1, frontier2, GROUP1_NAME, GROUP2_NAME,
            ASSET_GROUP_1, ASSET_GROUP_2
        )
    except Exception as e:
        print(f"保存Excel文件失败: {e}")
        excel_file = None

    # 6. 打印分析摘要
    print_analysis_summary(
        frontier1, frontier2, GROUP1_NAME, GROUP2_NAME,
        ASSET_GROUP_1, ASSET_GROUP_2
    )

    # 7. 输出文件信息
    if excel_file:
        print(f"\n分析完成！结果文件: {excel_file}")

    # 显示图片文件
    plot_files = [f for f in os.listdir('.') if f.startswith('有效前沿对比_') and f.endswith('.png')]
    if plot_files:
        latest_plot = max(plot_files, key=lambda x: os.path.getctime(x))
        print(f"图片文件: {latest_plot}")

if __name__ == "__main__":
    main()
