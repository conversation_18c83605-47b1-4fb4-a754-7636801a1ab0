import pandas as pd
import numpy as np
from datetime import datetime
import warnings
import os
warnings.filterwarnings('ignore')

print("=== 数字资产和传统资产指数分析 ===")

# 读取数据
print("正在读取数据文件...")
df = pd.read_excel('全部数据含债券合并.xlsx')
print(f"原始数据形状: {df.shape}")

# 转换日期列
df['Date'] = pd.to_datetime(df['Date'])

# 筛选2010年及以后的数据
df_filtered = df[df['Date'] >= '2011-01-01'].copy()
print(f"从2010年开始的数据形状: {df_filtered.shape}")
print(f"数据日期范围: {df_filtered['Date'].min().strftime('%Y-%m-%d')} 到 {df_filtered['Date'].max().strftime('%Y-%m-%d')}")

# 定义需要处理的列及其英文映射（加密货币用英文，其他用中文）
column_mapping = {
    '比特币': 'Bitcoin',
    '以太坊': 'Ethereum', 
    'XRP': 'Ripple',
    'MSCI全球指数_1': 'MSCI全球市场股票全收益',
    '中证800收益': '中证800全收益',
    '中证全债指数': '中证全债',
    '巴克莱全球债券指数': '巴克莱彭博全球债',
    'COMEX黄金': 'COMEX黄金',
    'NYMEX WTI原油': 'NYMEX WTI原油',
    '美元指数': '美元指数'
}

print("\n=== 检查数据可用性 ===")
# 检查哪些列存在且有有效数据
available_columns = []
for chinese_name, display_name in column_mapping.items():
    if chinese_name in df_filtered.columns:
        series = df_filtered[chinese_name]
        valid_count = series.notna().sum()
        
        # 找到第一个非零且非NaN值
        valid_non_zero_series = series[(series.notna()) & (series != 0)]
        non_zero_count = len(valid_non_zero_series)
        
        if non_zero_count > 50:  # 至少要有50个有效非零数据点
            available_columns.append((chinese_name, display_name))
            first_valid_idx = series.first_valid_index()
            first_date = df_filtered.loc[first_valid_idx, 'Date']
            
            if len(valid_non_zero_series) > 0:
                first_non_zero_idx = valid_non_zero_series.index[0]
                first_non_zero_date = df_filtered.loc[first_non_zero_idx, 'Date']
                first_non_zero_value = valid_non_zero_series.iloc[0]
                print(f"✓ {chinese_name} -> {display_name}")
                print(f"  有效数据点: {valid_count}, 有效非零数据点: {non_zero_count}")
                print(f"  开始日期: {first_date.strftime('%Y-%m-%d')}, 第一个非零值日期: {first_non_zero_date.strftime('%Y-%m-%d')}, 值: {first_non_zero_value:.4f}")
            else:
                print(f"✗ {chinese_name}: 没有有效非零数据")
        else:
            print(f"✗ {chinese_name}: 有效非零数据不足 ({non_zero_count}个)")
    else:
        print(f"✗ 未找到列: {chinese_name}")

if not available_columns:
    print("错误：未找到任何可用的数据列！")
    exit()

print(f"\n找到 {len(available_columns)} 个可用数据系列")

# 确保数据按日期排序
df_filtered = df_filtered.sort_values('Date').reset_index(drop=True)

# 创建原始指数数据
print("\n=== 创建原始指数数据 ===")
original_data = {'Date': df_filtered['Date']}

for chinese_name, display_name in available_columns:
    series = df_filtered[chinese_name].copy()
    
    # 对数据进行前值填充
    series_ffilled = series.ffill()
    original_data[display_name] = series_ffilled
    
    # 统计信息
    valid_count_before = series.notna().sum()
    valid_count_after = series_ffilled.notna().sum()
    print(f"{display_name}: 填充前有效数据 {valid_count_before}, 填充后有效数据 {valid_count_after}")

# 转换为DataFrame
original_df = pd.DataFrame(original_data)

# 创建归一化指数数据
print("\n=== 创建归一化指数数据 ===")
normalized_data = {'Date': df_filtered['Date']}

for chinese_name, display_name in available_columns:
    series = df_filtered[chinese_name].copy()
    
    # 对原始数据进行前值填充
    series_ffilled = series.ffill()
    
    # 找到第一个有效非零值
    valid_non_zero_series = series_ffilled[(series_ffilled.notna()) & (series_ffilled != 0)]
    if len(valid_non_zero_series) > 0:
        first_non_zero_idx = valid_non_zero_series.index[0]
        first_non_zero_value = valid_non_zero_series.iloc[0]
        first_non_zero_date = df_filtered.loc[first_non_zero_idx, 'Date']
        
        # 计算归一化指数（以第一个非零值为基数100）
        normalized_series = (series_ffilled / first_non_zero_value) * 100
        normalized_data[display_name] = normalized_series
        
        print(f"{display_name}: 基期 {first_non_zero_date.strftime('%Y-%m-%d')}, 基期值 {first_non_zero_value:.4f}")
    else:
        print(f"警告: {display_name} 列没有有效非零数据")
        normalized_data[display_name] = series_ffilled

# 转换为DataFrame
normalized_df = pd.DataFrame(normalized_data)

# 重新整理列顺序
column_order = ['Date'] + [display_name for _, display_name in available_columns]
original_df = original_df[column_order]
normalized_df = normalized_df[column_order]

# 保存到Excel文件
output_dir = '正文使用'
os.makedirs(output_dir, exist_ok=True)

# 保存原始数据
original_file = os.path.join(output_dir, '原始指数数据_2010起.xlsx')
original_df.to_excel(original_file, index=False)
print(f"\n原始指数数据已保存到: {original_file}")

# 保存归一化数据
normalized_file = os.path.join(output_dir, '归一化指数数据_2010起.xlsx')
normalized_df.to_excel(normalized_file, index=False)
print(f"归一化指数数据已保存到: {normalized_file}")

# 删除之前的画图文件
files_to_delete = [
    os.path.join(output_dir, '指数化数据_2010起.xlsx'),
    os.path.join(output_dir, '指数化图表_2010起.png')
]

for file_path in files_to_delete:
    if os.path.exists(file_path):
        os.remove(file_path)
        print(f"已删除文件: {file_path}")

# 显示最终统计
print("\n=== 最终统计 ===")
print(f"数据期间: {original_df['Date'].min().strftime('%Y-%m-%d')} 到 {original_df['Date'].max().strftime('%Y-%m-%d')}")
print(f"数据行数: {len(original_df)}")

print("\n原始数据各资产最新值:")
for _, display_name in available_columns:
    if display_name in original_df.columns:
        series = original_df[display_name]
        valid_data = series.dropna()
        if len(valid_data) > 0:
            latest_value = valid_data.iloc[-1]
            print(f"{display_name}: {latest_value:.4f}")

print("\n归一化数据各资产最终表现:")
for _, display_name in available_columns:
    if display_name in normalized_df.columns:
        series = normalized_df[display_name]
        valid_data = series.dropna()
        if len(valid_data) > 0:
            final_value = valid_data.iloc[-1]
            if not pd.isna(final_value) and final_value != 0:
                total_return = (final_value - 100) / 100 * 100
                print(f"{display_name}: 最终指数值 {final_value:.2f} (累计收益率: {total_return:+.1f}%)")

# 显示前值填充效果统计
print("\n=== 前值填充效果统计 ===")
for chinese_name, display_name in available_columns:
    if display_name in original_df.columns:
        series_original = df_filtered[chinese_name]  # 使用原始列名
        series_filled = original_df[display_name]
        
        na_count_before = series_original.isna().sum()
        na_count_after = series_filled.isna().sum()
        filled_count = na_count_before - na_count_after
        
        if filled_count > 0:
            print(f"{display_name}: 填充了 {filled_count} 个NaN值 ({na_count_before} -> {na_count_after})")
        else:
            print(f"{display_name}: 无需填充")

print("\n=== 处理完成! ===")
print("文件已保存到 '正文使用' 文件夹:")
print(f"- 原始数据: 原始指数数据_2010起.xlsx (已应用前值填充)")
print(f"- 归一化数据: 归一化指数数据_2010起.xlsx (已应用前值填充)")
print("- 已删除之前的画图相关文件") 