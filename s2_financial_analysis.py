import pandas as pd
import numpy as np
from scipy import stats
import warnings
import os

warnings.filterwarnings('ignore')

def load_and_inspect_data(data_path):
    """
    加载并检查数据集结构
    """
    print("=" * 60)
    print("数据加载和检查")
    print("=" * 60)
    
    try:
        # 读取Excel文件
        raw_data = pd.read_excel(data_path)
        
        print(f"数据文件路径: {data_path}")
        print(f"数据形状: {raw_data.shape}")
        print(f"\n列名:")
        for i, col in enumerate(raw_data.columns):
            print(f"  {i+1}. {col}")
            
        print(f"\n数据类型:")
        print(raw_data.dtypes)
        
        print(f"\n前5行数据:")
        print(raw_data.head())
        
        print(f"\n后5行数据:")
        print(raw_data.tail())
        
        # 检查缺失值
        missing_info = raw_data.isnull().sum()
        if missing_info.sum() > 0:
            print(f"\n缺失值情况:")
            print(missing_info[missing_info > 0])
        else:
            print(f"\n✓ 数据集无缺失值")
            
        # 检查数据时间范围
        if raw_data.iloc[:, 0].dtype == 'datetime64[ns]' or 'date' in str(raw_data.iloc[:, 0].dtype).lower():
            date_col = raw_data.iloc[:, 0]
            print(f"\n时间范围: {date_col.min()} 到 {date_col.max()}")
            print(f"总天数: {(date_col.max() - date_col.min()).days} 天")
        
        return raw_data
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

def calculate_basic_statistics(price_data):
    """
    计算基础统计指标：年化回报率和年化波动率
    """
    print("\n" + "=" * 60)
    print("基础统计计算")
    print("=" * 60)
    
    results = {}
    
    # 计算每个资产的年化回报率和波动率
    for asset in price_data.columns:
        prices = price_data[asset].dropna()
        
        if len(prices) < 2:
            continue
            
        # 计算年化回报率
        start_price = float(prices.iloc[0])
        end_price = float(prices.iloc[-1])
        start_date = prices.index[0]
        end_date = prices.index[-1]
        
        # 计算年数
        if hasattr(start_date, 'date'):
            days = (end_date.date() - start_date.date()).days
        else:
            days = (end_date - start_date).days if hasattr(end_date, 'days') else len(prices)
        
        years = days / 365.25
        
        if years > 0 and start_price > 0:
            annual_return = (end_price / start_price) ** (1/years) - 1
        else:
            annual_return = np.nan
        
        # 计算日度收益率
        daily_returns = prices.pct_change().dropna()
        
        # 计算年化波动率
        if len(daily_returns) > 1:
            annual_volatility = daily_returns.std() * np.sqrt(252)
        else:
            annual_volatility = np.nan
        
        results[asset] = {
            '年化回报率': annual_return,
            '年化波动率': annual_volatility,
            '数据天数': len(prices),
            '数据起始日期': start_date.strftime('%Y-%m-%d'),
            '数据结束日期': end_date.strftime('%Y-%m-%d'),
            '起始价格': start_price,
            '结束价格': end_price
        }
    
    # 创建结果DataFrame
    stats_df = pd.DataFrame(results).T
    
    print("各资产基础统计指标:")
    print(stats_df.round(4))
    
    return stats_df

def calculate_rolling_statistics(price_data, years=5):
    """
    计算滚动年化回报率和波动率的中位数
    """
    print(f"\n计算{years}年滚动统计...")
    window_days = int(years * 365.25)  # 5年真实天数：1826天
    
    rolling_results = {}
    
    for asset in price_data.columns:
        prices = price_data[asset].dropna()
        
        if len(prices) < window_days:
            print(f"⚠ {asset}: 数据长度不足{years}年，跳过滚动计算")
            continue
        
        rolling_returns = []
        rolling_volatilities = []
        
        # 计算滚动统计
        for i in range(window_days, len(prices)):
            window_prices = prices.iloc[i-window_days:i]
            
            # 滚动年化回报率
            start_price = window_prices.iloc[0]
            end_price = window_prices.iloc[-1]
            if start_price > 0:
                annual_return = (end_price / start_price) ** (1/years) - 1
                rolling_returns.append(annual_return)
            
            # 滚动年化波动率
            daily_returns = window_prices.pct_change().dropna()
            if len(daily_returns) > 1:
                annual_vol = daily_returns.std() * np.sqrt(365)
                rolling_volatilities.append(annual_vol)
        
        # 计算中位数
        roll_df = pd.DataFrame({'rolling_returns': rolling_returns, 'rolling_volatilities': rolling_volatilities})
        roll_df = roll_df.sort_values('rolling_returns')
        loc = len(roll_df)//2
        median_return = roll_df['rolling_returns'].iloc[loc]
        median_volatility = roll_df['rolling_volatilities'].iloc[loc]
        rolling_results[asset] = {
                f'{years}年滚动年化回报率中位数': median_return,
                f'{years}年滚动年化波动率中位数': median_volatility,
                '滚动期间数量': len(rolling_returns)
            }

           
            
    
    if rolling_results:
        rolling_df = pd.DataFrame(rolling_results).T
        print(f"\n{years}年滚动统计中位数:")
        print(rolling_df.round(4))
        return rolling_df
    else:
        print(f"⚠ 没有足够数据进行{years}年滚动计算")
        return None

def calculate_returns(price_data):
    """
    计算日度收益率
    """
    print("\n" + "=" * 60)
    print("收益率计算")
    print("=" * 60)
    
    # 计算简单收益率
    returns_data = price_data.pct_change().dropna()
    
    print(f"收益率数据形状: {returns_data.shape}")
    print(f"收益率统计摘要:")
    print(returns_data.describe().round(4))
    
    # 检查极端值
    print(f"\n收益率极值检查:")
    for asset in returns_data.columns:
        returns = returns_data[asset]
        max_return = returns.max()
        min_return = returns.min()
        print(f"{asset}: 最大收益率 {max_return:.4f}, 最小收益率 {min_return:.4f}")
    
    return returns_data

def calculate_correlations(returns_data):
    """
    计算相关性分析
    """
    print("\n" + "=" * 60)
    print("相关性分析")
    print("=" * 60)
    
    if returns_data is None:
        print("⚠ 需要先计算收益率数据")
        return None, None
    
    # Pearson线性相关系数
    pearson_corr = returns_data.corr(method='pearson')
    print("✓ Pearson线性相关系数矩阵计算完成")
    
    # Spearman等级相关系数
    spearman_corr = returns_data.corr(method='spearman')
    print("✓ Spearman等级相关系数矩阵计算完成")
    
    # 显著性检验 (Pearson)
    n = len(returns_data)
    
    print(f"\n相关性统计摘要:")
    print(f"样本数量: {n}")
    
    # 找出最高和最低相关性的资产对
    corr_pairs = []
    assets = list(returns_data.columns)
    
    for i in range(len(assets)):
        for j in range(i+1, len(assets)):
            asset1, asset2 = assets[i], assets[j]
            corr_value = pearson_corr.loc[asset1, asset2]
            corr_pairs.append((asset1, asset2, corr_value))
    
    # 排序找出极值
    corr_pairs.sort(key=lambda x: abs(x[2]), reverse=True)
    
    print(f"\n相关性最高的资产对 (前5个):")
    for i in range(min(5, len(corr_pairs))):
        asset1, asset2, corr = corr_pairs[i]
        print(f"  {asset1} - {asset2}: {corr:.4f}")
    
    print(f"\n相关性最低的资产对 (后5个):")
    for i in range(max(0, len(corr_pairs)-5), len(corr_pairs)):
        asset1, asset2, corr = corr_pairs[i]
        print(f"  {asset1} - {asset2}: {corr:.4f}")
    
    return pearson_corr, spearman_corr

def create_professional_risk_return_table(stats_df, rolling_df):
    """
    创建专业的风险收益特征表格
    """
    # 合并基础统计和滚动统计
    result_table = pd.DataFrame(index=stats_df.index)
    
    # 数据期间信息
    result_table['数据起始日期'] = stats_df['数据起始日期']
    result_table['数据结束日期'] = stats_df['数据结束日期']
    result_table['数据天数'] = stats_df['数据天数']
    
    # 整体期间数据
    result_table['年化回报率'] = (stats_df['年化回报率'].fillna(0) * 100).round(2).astype(str) + '%'
    result_table['年化波动率'] = (stats_df['年化波动率'].fillna(0) * 100).round(2).astype(str) + '%'

    # 任意连续5年中位数
    if rolling_df is not None:
        result_table['5年滚动回报率中位数'] = (rolling_df['5年滚动年化回报率中位数'].fillna(0) * 100).round(2).astype(str) + '%'
        result_table['5年滚动波动率中位数'] = (rolling_df['5年滚动年化波动率中位数'].fillna(0) * 100).round(2).astype(str) + '%'
    else:
        result_table['5年滚动回报率中位数'] = 'N/A'
        result_table['5年滚动波动率中位数'] = 'N/A'
    
    # 重新排列资产顺序，按重要性排序
    asset_order = ['Bitcoin', 'Ethereum', 'Ripple', 'MSCI全球市场股票全收益', 
                   '中证800全收益', '中证全债', '巴克莱彭博全球债', 'COMEX黄金', 
                   'NYMEX WTI原油', '美元指数']
    
    # 过滤存在的资产
    existing_assets = [asset for asset in asset_order if asset in result_table.index]
    result_table = result_table.reindex(existing_assets)
    
    return result_table

def create_professional_correlation_table(pearson_corr):
    """
    创建专业的相关性表格
    """
    # 创建相关性矩阵，只显示下三角
    corr_matrix = pearson_corr.copy()
    
    # 将上三角设为空
    mask = np.triu(np.ones_like(corr_matrix), k=1).astype(bool)
    corr_matrix = corr_matrix.mask(mask)
    
    # 格式化数据
    formatted_corr = corr_matrix.fillna('').round(2)
    
    return formatted_corr

def save_to_excel(price_data, stats_df, rolling_df, returns_data, pearson_corr, spearman_corr):
    """
    将所有分析结果保存到Excel文件
    """
    print("\n" + "=" * 60)
    print("保存结果到Excel")
    print("=" * 60)
    
    try:
        # 创建一个包含所有结果的字典
        excel_data = {}
        
        # 1. 专业风险收益特征表（表1）
        professional_risk_return = create_professional_risk_return_table(stats_df, rolling_df)
        excel_data['表1-各大类资产风险收益特征'] = professional_risk_return
        print("✓ 表1-各大类资产风险收益特征准备完成")
        
        # 2. 专业相关性表（表2）
        professional_correlation = create_professional_correlation_table(pearson_corr)
        excel_data['表2-各大类资产回报率相关性'] = professional_correlation
        print("✓ 表2-各大类资产回报率相关性准备完成")
        
        # 3. 基础统计指标（详细数据）
        excel_data['基础统计指标'] = stats_df
        print("✓ 基础统计指标准备完成")
        
        # 4. 滚动统计指标
        if rolling_df is not None:
            excel_data['5年滚动统计'] = rolling_df
            print("✓ 5年滚动统计准备完成")
        
        # 5. 收益率统计摘要
        returns_summary = returns_data.describe()
        excel_data['收益率统计摘要'] = returns_summary
        print("✓ 收益率统计摘要准备完成")
        
        # 6. Pearson相关性矩阵
        excel_data['Pearson相关性'] = pearson_corr
        print("✓ Pearson相关性矩阵准备完成")
        
        # 7. Spearman相关性矩阵
        excel_data['Spearman相关性'] = spearman_corr
        print("✓ Spearman相关性矩阵准备完成")
        
        # 8. 价格数据样本
        price_sample = price_data.tail(100)
        excel_data['价格数据样本'] = price_sample
        print("✓ 价格数据样本准备完成")
        
        # 9. 收益率数据样本
        returns_sample = returns_data.tail(100)
        excel_data['收益率数据样本'] = returns_sample
        print("✓ 收益率数据样本准备完成")
        
        # 10. 相关性分析汇总
        correlation_summary = create_correlation_summary(pearson_corr, spearman_corr)
        excel_data['相关性分析汇总'] = correlation_summary
        print("✓ 相关性分析汇总准备完成")
        
        # 11. 夏普比率计算
        sharpe_df = calculate_sharpe_ratios(stats_df)
        excel_data['夏普比率'] = sharpe_df
        print("✓ 夏普比率准备完成")
        
        # 保存到Excel文件
        excel_filename = 'financial_analysis_results.xlsx'
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            for sheet_name, data in excel_data.items():
                data.to_excel(writer, sheet_name=sheet_name, index=True)
        print(f"\n✓ 所有结果已保存到 '{excel_filename}'")
        return excel_filename
    
    except Exception as e:
        print(f"⚠ Excel保存过程中出现错误: {e}")
        print("错误详情:", str(e))
        return None

def create_correlation_summary(pearson_corr, spearman_corr):
    """
    创建相关性分析汇总表
    """
    assets = list(pearson_corr.columns)
    summary_data = []
    
    for i in range(len(assets)):
        for j in range(i+1, len(assets)):
            asset1, asset2 = assets[i], assets[j]
            pearson_val = pearson_corr.loc[asset1, asset2]
            spearman_val = spearman_corr.loc[asset1, asset2]
            
            # 分类相关性强度
            if abs(pearson_val) >= 0.7:
                correlation_level = "强相关"
            elif abs(pearson_val) >= 0.5:
                correlation_level = "中等相关"
            elif abs(pearson_val) >= 0.3:
                correlation_level = "弱相关"
            else:
                correlation_level = "无相关"
            
            summary_data.append({
                '资产对': f"{asset1} - {asset2}",
                'Pearson相关系数': round(pearson_val, 4),
                'Spearman相关系数': round(spearman_val, 4),
                '相关性强度': correlation_level,
                '绝对值': round(abs(pearson_val), 4)
            })
    
    # 按绝对相关性排序
    summary_df = pd.DataFrame(summary_data)
    summary_df = summary_df.sort_values('绝对值', ascending=False)
    
    return summary_df

def calculate_sharpe_ratios(stats_df, risk_free_rate=0.02):
    """
    计算夏普比率
    """
    sharpe_data = {}
    
    for asset in stats_df.index:
        annual_return = stats_df.loc[asset, '年化回报率']
        annual_volatility = stats_df.loc[asset, '年化波动率']
        
        if not pd.isna(annual_return) and not pd.isna(annual_volatility) and annual_volatility > 0:
            sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility
        else:
            sharpe_ratio = np.nan
        
        sharpe_data[asset] = {
            '年化回报率': float(annual_return) if not pd.isna(annual_return) else np.nan,
            '年化波动率': float(annual_volatility) if not pd.isna(annual_volatility) else np.nan,
            '无风险利率': risk_free_rate,
            '超额收益率': float(annual_return - risk_free_rate) if not pd.isna(annual_return) else np.nan,
            '夏普比率': float(sharpe_ratio) if not pd.isna(sharpe_ratio) else np.nan
        }
    
    sharpe_df = pd.DataFrame(sharpe_data).T
    # 按夏普比率排序
    sharpe_df = sharpe_df.sort_values('夏普比率', ascending=False)
    
    return sharpe_df



def main():
    """
    主函数 - 执行完整的金融分析流程
    """
    print("开始执行完整金融分析...")
    print("=" * 80)
    
    # 数据文件路径
    data_path = "正文使用/原始指数数据_2010起.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(data_path):
        print(f"❌ 数据文件不存在: {data_path}")
        print("请确认文件路径是否正确")
        return
    
    # 1. 数据加载和检查
    raw_data = load_and_inspect_data(data_path)
    if raw_data is None:
        return
    
    # 2. 数据预处理
    # 将第一列作为日期索引
    date_col = raw_data.columns[0]  # 通常是'Date'
    raw_data[date_col] = pd.to_datetime(raw_data[date_col])
    raw_data = raw_data.set_index(date_col)
    
    # 重新索引为连续日期并向前填充
    index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D') 
    raw_data = raw_data.reindex(index_new)  
    raw_data = raw_data.fillna(method='ffill')
    # raw_data = raw_data.dropna()
    
    # 3. 准备价格数据（已经有日期索引）
    price_data = raw_data.copy()
    
    # 4. 基础统计计算
    stats_df = calculate_basic_statistics(price_data)
    
    # 5. 滚动统计计算
    rolling_df = calculate_rolling_statistics(price_data, 5)
    
    # 6. 收益率计算
    returns_data = calculate_returns(price_data)
    
    # 7. 相关性分析
    pearson_corr, spearman_corr = calculate_correlations(returns_data)
    
    # 8. 保存到Excel
    if stats_df is not None and pearson_corr is not None:
        excel_filename = save_to_excel(price_data, stats_df, rolling_df, returns_data, pearson_corr, spearman_corr)
    
    print("\n" + "=" * 80)
    print("✓ 完整金融分析已完成!")
    print("生成的文件:")
    print(f"  - {excel_filename} (Excel分析结果)")
    print("=" * 80)

if __name__ == "__main__":
    main() 