#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
滚动相关性分析脚本
功能：计算两个资产之间的滚动相关性分析
作者：AI Assistant
日期：2025-07-03
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from openpyxl import Workbook
from openpyxl.chart import LineChart, Reference
from openpyxl.chart.axis import DateAxis
from openpyxl.chart.marker import DataPoint
from openpyxl.drawing.colors import ColorChoice
from openpyxl.styles import Font, PatternFill
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# ==================== 配置参数 ====================
# 可配置的资产名称（请根据实际数据列名修改）
ASSET_1 = "中证800全收益"  # 第一个资产名称
ASSET_2 = "MSCI全球市场股票全收益"  # 第二个资产名称

# 可配置的回看窗口参数（天数）
ROLLING_WINDOW = 365*2  # 滚动窗口大小，可选：30, 60, 90等

# 数据文件路径
DATA_FILE = "正文使用/原始指数数据_2010起.xlsx"

# 输出文件路径
OUTPUT_FILE = f"滚动相关性_{ASSET_1}_{ASSET_2}_{ROLLING_WINDOW}天.xlsx"

def load_and_preprocess_data(file_path):
    """
    读取并预处理数据
    
    Parameters:
    file_path (str): 数据文件路径
    
    Returns:
    pd.DataFrame: 预处理后的数据
    """
    print("正在读取数据文件...")
    
    try:
        # 读取Excel文件
        raw_data = pd.read_excel(file_path)
        print(f"成功读取数据，原始数据形状: {raw_data.shape}")
        
        # 显示前几行数据以便检查
        print("\n原始数据前5行:")
        print(raw_data.head())
        print("\n数据列名:")
        print(raw_data.columns.tolist())
        
        # 日期列处理
        date_col = raw_data.columns[0]  # 通常是'Date'或类似名称
        print(f"\n使用日期列: {date_col}")
        
        # 转换日期格式
        raw_data[date_col] = pd.to_datetime(raw_data[date_col])
        raw_data = raw_data.set_index(date_col)
        
        # 重新索引为连续日期并向前填充
        print("正在进行日期补全处理...")
        index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D') 
        raw_data = raw_data.reindex(index_new)  
        raw_data = raw_data.fillna(method='ffill')
        
        print(f"日期补全后数据形状: {raw_data.shape}")
        print(f"数据日期范围: {raw_data.index[0]} 到 {raw_data.index[-1]}")
        
        return raw_data
        
    except Exception as e:
        print(f"读取数据时发生错误: {e}")
        return None

def check_asset_columns(data, asset1, asset2):
    """
    检查指定的资产列是否存在于数据中
    
    Parameters:
    data (pd.DataFrame): 数据框
    asset1 (str): 第一个资产名称
    asset2 (str): 第二个资产名称
    
    Returns:
    tuple: (是否存在, 可用列名列表)
    """
    available_columns = data.columns.tolist()
    
    asset1_exists = asset1 in available_columns
    asset2_exists = asset2 in available_columns
    
    if not asset1_exists:
        print(f"警告: 未找到资产 '{asset1}'")
    if not asset2_exists:
        print(f"警告: 未找到资产 '{asset2}'")
    
    if not (asset1_exists and asset2_exists):
        print("\n可用的列名:")
        for i, col in enumerate(available_columns):
            print(f"{i+1}. {col}")
    
    return (asset1_exists and asset2_exists), available_columns

def calculate_returns(data, asset1, asset2):
    """
    计算资产的收益率
    
    Parameters:
    data (pd.DataFrame): 价格数据
    asset1 (str): 第一个资产名称
    asset2 (str): 第二个资产名称
    
    Returns:
    pd.DataFrame: 包含价格和收益率的数据框
    """
    print("正在计算收益率...")
    
    # 创建结果数据框
    result_df = pd.DataFrame(index=data.index)
    
    # 添加价格数据
    result_df[f'{asset1}_价格'] = data[asset1]
    result_df[f'{asset2}_价格'] = data[asset2]
    
    # 计算日收益率（百分比）
    result_df[f'{asset1}_收益率'] = data[asset1].pct_change() * 100
    result_df[f'{asset2}_收益率'] = data[asset2].pct_change() * 100
    
    # 删除第一行（收益率为NaN）
    result_df = result_df.dropna()
    
    print(f"收益率计算完成，有效数据点: {len(result_df)}")
    
    return result_df

def calculate_rolling_correlation(data, asset1, asset2, window):
    """
    计算滚动相关系数
    
    Parameters:
    data (pd.DataFrame): 包含收益率的数据框
    asset1 (str): 第一个资产名称
    asset2 (str): 第二个资产名称
    window (int): 滚动窗口大小
    
    Returns:
    pd.Series: 滚动相关系数序列
    """
    print(f"正在计算{window}天滚动相关系数...")
    
    # 获取收益率列
    returns1 = data[f'{asset1}_收益率']
    returns2 = data[f'{asset2}_收益率']
    
    # 计算滚动相关系数
    rolling_corr = returns1.rolling(window=window).corr(returns2)
    
    # 删除前面的NaN值
    rolling_corr = rolling_corr.dropna()
    
    print(f"滚动相关系数计算完成，有效数据点: {len(rolling_corr)}")
    print(f"相关系数范围: {rolling_corr.min():.4f} 到 {rolling_corr.max():.4f}")
    print(f"平均相关系数: {rolling_corr.mean():.4f}")
    
    return rolling_corr

def create_final_results(data, rolling_corr, asset1, asset2, window):
    """
    创建最终结果数据框
    
    Parameters:
    data (pd.DataFrame): 原始数据
    rolling_corr (pd.Series): 滚动相关系数
    asset1 (str): 第一个资产名称
    asset2 (str): 第二个资产名称
    window (int): 滚动窗口大小
    
    Returns:
    pd.DataFrame: 最终结果数据框
    """
    print("正在整理最终结果...")
    
    # 创建结果数据框，使用滚动相关系数的索引
    final_results = pd.DataFrame(index=rolling_corr.index)
    
    # 添加日期列（重置索引）
    final_results['日期'] = final_results.index
    
    # 添加价格数据
    final_results[f'{asset1}_价格'] = data.loc[rolling_corr.index, f'{asset1}_价格']
    final_results[f'{asset2}_价格'] = data.loc[rolling_corr.index, f'{asset2}_价格']
    
    # 添加收益率数据
    final_results[f'{asset1}_收益率(%)'] = data.loc[rolling_corr.index, f'{asset1}_收益率']
    final_results[f'{asset2}_收益率(%)'] = data.loc[rolling_corr.index, f'{asset2}_收益率']
    
    # 添加滚动相关系数
    final_results[f'{window}天滚动相关系数'] = rolling_corr
    
    # 重置索引，使日期成为普通列
    final_results = final_results.reset_index(drop=True)
    
    print(f"最终结果整理完成，共{len(final_results)}行数据")
    
    return final_results

def create_charts_and_save_excel(results, output_file, asset1, asset2, window):
    """
    创建图表并保存到Excel文件

    Parameters:
    results (pd.DataFrame): 结果数据框
    output_file (str): 输出文件路径
    asset1 (str): 第一个资产名称
    asset2 (str): 第二个资产名称
    window (int): 滚动窗口大小
    """
    print(f"正在创建图表并保存到文件: {output_file}")

    try:
        # 准备数据
        corr_col = f'{window}天滚动相关系数'

        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        fig.suptitle(f'{asset1} vs {asset2} 滚动相关性分析 (窗口: {window}天)', fontsize=16, fontweight='bold')

        # 图1: 滚动相关系数时间序列
        ax1 = axes[0]
        ax1.plot(results['日期'], results[corr_col], linewidth=1.5, color='blue', alpha=0.8)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax1.axhline(y=0.5, color='red', linestyle='--', alpha=0.7, label='强正相关线(0.5)')
        ax1.axhline(y=-0.5, color='red', linestyle='--', alpha=0.7, label='强负相关线(-0.5)')
        ax1.fill_between(results['日期'], results[corr_col], 0,
                        where=(results[corr_col] > 0.5), alpha=0.3, color='green', label='强正相关区域')
        ax1.fill_between(results['日期'], results[corr_col], 0,
                        where=(results[corr_col] < -0.5), alpha=0.3, color='red', label='强负相关区域')

        ax1.set_title(f'{window}天滚动相关系数时间序列', fontsize=14, fontweight='bold')
        ax1.set_ylabel('相关系数', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend(loc='upper right')
        ax1.set_ylim(-1, 1)

        # 添加重要事件标注
        max_corr_idx = results[corr_col].idxmax()
        min_corr_idx = results[corr_col].idxmin()

        ax1.annotate(f'最高相关性\n{results.loc[max_corr_idx, corr_col]:.3f}',
                    xy=(results.loc[max_corr_idx, '日期'], results.loc[max_corr_idx, corr_col]),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        ax1.annotate(f'最低相关性\n{results.loc[min_corr_idx, corr_col]:.3f}',
                    xy=(results.loc[min_corr_idx, '日期'], results.loc[min_corr_idx, corr_col]),
                    xytext=(10, -20), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='orange', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))

        # 图2: 相关性分布直方图
        ax2 = axes[1]
        n_bins = 30
        _, bins, patches = ax2.hist(results[corr_col], bins=n_bins, alpha=0.7, color='skyblue', edgecolor='black')

        # 为不同相关性区间着色
        for bin_left, bin_right, patch in zip(bins[:-1], bins[1:], patches):
            bin_center = (bin_left + bin_right) / 2
            if bin_center > 0.5:
                patch.set_facecolor('green')
                patch.set_alpha(0.7)
            elif bin_center < -0.5:
                patch.set_facecolor('red')
                patch.set_alpha(0.7)
            elif bin_center > 0.3:
                patch.set_facecolor('lightgreen')
                patch.set_alpha(0.7)
            elif bin_center < -0.3:
                patch.set_facecolor('lightcoral')
                patch.set_alpha(0.7)

        ax2.axvline(x=results[corr_col].mean(), color='red', linestyle='-', linewidth=2,
                   label=f'平均值: {results[corr_col].mean():.3f}')
        ax2.axvline(x=0, color='black', linestyle='--', alpha=0.5)

        ax2.set_title('相关系数分布直方图', fontsize=14, fontweight='bold')
        ax2.set_xlabel('相关系数', fontsize=12)
        ax2.set_ylabel('频数', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # 图3: 资产价格走势对比
        ax3 = axes[2]

        # 标准化价格以便比较
        price1_norm = results[f'{asset1}_价格'] / results[f'{asset1}_价格'].iloc[0] * 100
        price2_norm = results[f'{asset2}_价格'] / results[f'{asset2}_价格'].iloc[0] * 100

        ax3.plot(results['日期'], price1_norm, label=f'{asset1} (标准化)', linewidth=1.5, color='blue')
        ax3.plot(results['日期'], price2_norm, label=f'{asset2} (标准化)', linewidth=1.5, color='red')

        ax3.set_title('资产价格走势对比 (标准化至起始点=100)', fontsize=14, fontweight='bold')
        ax3.set_xlabel('日期', fontsize=12)
        ax3.set_ylabel('标准化价格', fontsize=12)
        ax3.grid(True, alpha=0.3)
        ax3.legend()

        # 调整布局
        plt.tight_layout()

        # 保存图表为临时文件
        chart_file = output_file.replace('.xlsx', '_chart.png')
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()

        print("图表创建完成，正在保存到Excel...")

        # 保存到Excel
        save_results_to_excel_with_charts(results, output_file, asset1, asset2, window, chart_file)

    except Exception as e:
        print(f"创建图表时发生错误: {e}")

def save_results_to_excel_with_charts(results, output_file, asset1, asset2, window, chart_file):
    """
    将结果和图表保存到Excel文件
    """
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 保存主要结果
            results_copy = results.copy()
            results_copy['日期'] = results_copy['日期'].dt.strftime('%Y-%m-%d')
            results_copy.to_excel(writer, sheet_name='滚动相关性分析', index=False)

            # 创建统计摘要
            corr_col = f'{window}天滚动相关系数'
            summary_stats = pd.DataFrame({
                '统计指标': ['数据点数量', '平均相关系数', '最大相关系数', '最小相关系数',
                          '相关系数标准差', '数据开始日期', '数据结束日期', '高相关期间(>0.5)', '低相关期间(<0.1)'],
                '数值': [
                    len(results),
                    f"{results[corr_col].mean():.4f}",
                    f"{results[corr_col].max():.4f}",
                    f"{results[corr_col].min():.4f}",
                    f"{results[corr_col].std():.4f}",
                    results['日期'].min().strftime('%Y-%m-%d'),
                    results['日期'].max().strftime('%Y-%m-%d'),
                    f"{(results[corr_col] > 0.5).sum()} 天 ({(results[corr_col] > 0.5).mean()*100:.1f}%)",
                    f"{(results[corr_col] < 0.1).sum()} 天 ({(results[corr_col] < 0.1).mean()*100:.1f}%)"
                ]
            })
            summary_stats.to_excel(writer, sheet_name='统计摘要', index=False)

            # 创建相关性分布统计
            bins = [-1, -0.5, -0.1, 0.1, 0.3, 0.5, 0.7, 1]
            labels = ['强负相关(<-0.5)', '中等负相关(-0.5~-0.1)', '弱负相关(-0.1~0.1)',
                     '弱正相关(0.1~0.3)', '中等正相关(0.3~0.5)', '强正相关(0.5~0.7)', '极强正相关(>0.7)']

            corr_distribution = pd.cut(results[corr_col], bins=bins, labels=labels, include_lowest=True)
            dist_counts = pd.Series(corr_distribution).value_counts().sort_index()
            dist_pct = (dist_counts / len(results) * 100).round(1)

            distribution_df = pd.DataFrame({
                '相关性区间': dist_counts.index,
                '天数': dist_counts.values,
                '占比(%)': dist_pct.values
            })
            distribution_df.to_excel(writer, sheet_name='相关性分布', index=False)

            # 创建参数说明
            params_info = pd.DataFrame({
                '参数': ['资产1', '资产2', '滚动窗口(天)', '分析日期', '数据来源'],
                '值': [asset1, asset2, window, pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'), DATA_FILE]
            })
            params_info.to_excel(writer, sheet_name='参数说明', index=False)

            # 创建图表工作表
            chart_df = pd.DataFrame({'说明': ['图表已保存为单独的PNG文件', f'文件名: {chart_file}',
                                           '图表包含:', '1. 滚动相关系数时间序列', '2. 相关系数分布直方图', '3. 资产价格走势对比']})
            chart_df.to_excel(writer, sheet_name='图表说明', index=False)

        # 尝试在Excel中嵌入图表（使用openpyxl）
        try:
            from openpyxl import load_workbook
            from openpyxl.drawing.image import Image

            wb = load_workbook(output_file)

            # 创建图表工作表
            if '图表' not in wb.sheetnames:
                chart_ws = wb.create_sheet('图表')
            else:
                chart_ws = wb['图表']

            # 插入图片
            img = Image(chart_file)
            img.width = 1200  # 调整图片大小
            img.height = 900
            chart_ws.add_image(img, 'A1')

            wb.save(output_file)
            print(f"图表已嵌入Excel文件")

        except Exception as e:
            print(f"嵌入图表到Excel时出现问题: {e}")
            print("图表已单独保存为PNG文件")

        print(f"结果已成功保存到: {output_file}")

    except Exception as e:
        print(f"保存文件时发生错误: {e}")

def print_analysis_summary(results, asset1, asset2, window):
    """
    打印分析结果摘要

    Parameters:
    results (pd.DataFrame): 结果数据框
    asset1 (str): 第一个资产名称
    asset2 (str): 第二个资产名称
    window (int): 滚动窗口大小
    """
    corr_col = f'{window}天滚动相关系数'

    print("\n" + "=" * 60)
    print("分析结果摘要")
    print("=" * 60)
    print(f"资产对: {asset1} vs {asset2}")
    print(f"滚动窗口: {window}天")
    print(f"分析期间: {results['日期'].min().strftime('%Y-%m-%d')} 至 {results['日期'].max().strftime('%Y-%m-%d')}")
    print(f"有效数据点: {len(results)}天")
    print("-" * 60)
    print("相关性统计:")
    print(f"  平均相关系数: {results[corr_col].mean():.4f}")
    print(f"  最大相关系数: {results[corr_col].max():.4f}")
    print(f"  最小相关系数: {results[corr_col].min():.4f}")
    print(f"  标准差: {results[corr_col].std():.4f}")
    print("-" * 60)
    print("相关性分布:")
    print(f"  强正相关(>0.5): {(results[corr_col] > 0.5).sum()}天 ({(results[corr_col] > 0.5).mean()*100:.1f}%)")
    print(f"  中等正相关(0.3-0.5): {((results[corr_col] >= 0.3) & (results[corr_col] <= 0.5)).sum()}天 ({((results[corr_col] >= 0.3) & (results[corr_col] <= 0.5)).mean()*100:.1f}%)")
    print(f"  弱相关(-0.1-0.3): {((results[corr_col] >= -0.1) & (results[corr_col] <= 0.3)).sum()}天 ({((results[corr_col] >= -0.1) & (results[corr_col] <= 0.3)).mean()*100:.1f}%)")
    print(f"  负相关(<-0.1): {(results[corr_col] < -0.1).sum()}天 ({(results[corr_col] < -0.1).mean()*100:.1f}%)")
    print("=" * 60)

def main():
    """
    主函数：执行完整的滚动相关性分析流程
    """
    print("=" * 60)
    print("滚动相关性分析脚本")
    print("=" * 60)
    print(f"分析资产: {ASSET_1} vs {ASSET_2}")
    print(f"滚动窗口: {ROLLING_WINDOW}天")
    print("=" * 60)
    
    # 1. 读取和预处理数据
    data = load_and_preprocess_data(DATA_FILE)
    if data is None:
        print("数据读取失败，程序终止")
        return
    
    # 2. 检查资产列是否存在
    assets_exist, available_columns = check_asset_columns(data, ASSET_1, ASSET_2)
    if not assets_exist:
        print("指定的资产列不存在，请检查配置参数")
        return
    
    # 3. 计算收益率
    returns_data = calculate_returns(data, ASSET_1, ASSET_2)
    
    # 4. 计算滚动相关系数
    rolling_corr = calculate_rolling_correlation(returns_data, ASSET_1, ASSET_2, ROLLING_WINDOW)
    
    # 5. 创建最终结果
    final_results = create_final_results(returns_data, rolling_corr, ASSET_1, ASSET_2, ROLLING_WINDOW)
    
    # 6. 创建图表并保存结果到Excel
    create_charts_and_save_excel(final_results, OUTPUT_FILE, ASSET_1, ASSET_2, ROLLING_WINDOW)

    # 7. 打印分析摘要
    print_analysis_summary(final_results, ASSET_1, ASSET_2, ROLLING_WINDOW)

    print("\n" + "=" * 60)
    print("分析完成！")
    print(f"结果文件: {OUTPUT_FILE}")
    print("=" * 60)

if __name__ == "__main__":
    main()
