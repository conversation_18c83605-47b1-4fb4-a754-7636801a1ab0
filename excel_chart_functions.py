import pandas as pd
from openpyxl import load_workbook
from openpyxl.chart import LineChart, Reference, BarChart
from openpyxl.chart.axis import DateAxis
from openpyxl.styles import Font

def save_results_to_excel_with_native_charts(results, output_file, asset1, asset2, window, data_file):
    """
    将结果保存到Excel文件并创建原生Excel图表
    
    Parameters:
    results (pd.DataFrame): 结果数据框
    output_file (str): 输出文件路径
    asset1 (str): 第一个资产名称
    asset2 (str): 第二个资产名称
    window (int): 滚动窗口大小
    data_file (str): 数据源文件名
    """
    print(f"正在创建Excel原生图表并保存到文件: {output_file}")
    
    try:
        # 准备数据
        corr_col = f'{window}天滚动相关系数'
        
        # 保存基础数据到Excel
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 保存主要结果
            results_copy = results.copy()
            results_copy['日期'] = results_copy['日期'].dt.strftime('%Y-%m-%d')
            results_copy.to_excel(writer, sheet_name='滚动相关性分析', index=False)

            # 创建统计摘要
            summary_stats = pd.DataFrame({
                '统计指标': ['数据点数量', '平均相关系数', '最大相关系数', '最小相关系数',
                          '相关系数标准差', '数据开始日期', '数据结束日期', '高相关期间(>0.5)', '低相关期间(<0.1)'],
                '数值': [
                    len(results),
                    f"{results[corr_col].mean():.4f}",
                    f"{results[corr_col].max():.4f}",
                    f"{results[corr_col].min():.4f}",
                    f"{results[corr_col].std():.4f}",
                    results['日期'].min().strftime('%Y-%m-%d'),
                    results['日期'].max().strftime('%Y-%m-%d'),
                    f"{(results[corr_col] > 0.5).sum()} 天 ({(results[corr_col] > 0.5).mean()*100:.1f}%)",
                    f"{(results[corr_col] < 0.1).sum()} 天 ({(results[corr_col] < 0.1).mean()*100:.1f}%)"
                ]
            })
            summary_stats.to_excel(writer, sheet_name='统计摘要', index=False)

            # 创建相关性分布统计
            bins = [-1, -0.5, -0.1, 0.1, 0.3, 0.5, 0.7, 1]
            labels = ['强负相关(<-0.5)', '中等负相关(-0.5~-0.1)', '弱负相关(-0.1~0.1)',
                     '弱正相关(0.1~0.3)', '中等正相关(0.3~0.5)', '强正相关(0.5~0.7)', '极强正相关(>0.7)']

            corr_distribution = pd.cut(results[corr_col], bins=bins, labels=labels, include_lowest=True)
            dist_counts = pd.Series(corr_distribution).value_counts().sort_index()
            dist_pct = (dist_counts / len(results) * 100).round(1)

            distribution_df = pd.DataFrame({
                '相关性区间': dist_counts.index,
                '天数': dist_counts.values,
                '占比(%)': dist_pct.values
            })
            distribution_df.to_excel(writer, sheet_name='相关性分布', index=False)

            # 创建参数说明
            params_info = pd.DataFrame({
                '参数': ['资产1', '资产2', '滚动窗口(天)', '分析日期', '数据来源'],
                '值': [asset1, asset2, window, pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'), data_file]
            })
            params_info.to_excel(writer, sheet_name='参数说明', index=False)
            
            # 为图表准备数据工作表
            chart_data = results_copy[['日期', corr_col, f'{asset1}_价格', f'{asset2}_价格']].copy()
            # 标准化价格数据
            chart_data[f'{asset1}_标准化价格'] = chart_data[f'{asset1}_价格'] / chart_data[f'{asset1}_价格'].iloc[0] * 100
            chart_data[f'{asset2}_标准化价格'] = chart_data[f'{asset2}_价格'] / chart_data[f'{asset2}_价格'].iloc[0] * 100
            chart_data.to_excel(writer, sheet_name='图表数据', index=False)

        # 使用openpyxl添加原生Excel图表
        wb = load_workbook(output_file)

        # 创建图表工作表
        if '相关性图表' in wb.sheetnames:
            del wb['相关性图表']
        chart_ws = wb.create_sheet('相关性图表')

        # 获取图表数据工作表
        data_ws = wb['图表数据']

        # 计算数据行数
        max_row = data_ws.max_row

        # 图表1: 滚动相关系数时间序列图
        chart1 = LineChart()
        chart1.title = f"{asset1} vs {asset2} 滚动相关性时间序列"
        chart1.style = 13
        chart1.y_axis.title = '相关系数'
        chart1.x_axis.title = '日期'
        chart1.width = 20
        chart1.height = 12
        
        # 添加相关系数数据
        corr_data = Reference(data_ws, min_col=2, min_row=1, max_col=2, max_row=max_row)
        dates = Reference(data_ws, min_col=1, min_row=2, max_row=max_row)
        
        chart1.add_data(corr_data, titles_from_data=True)
        chart1.set_categories(dates)
        
        # 设置系列样式
        s1 = chart1.series[0]
        s1.graphicalProperties.line.solidFill = "0066CC"
        s1.graphicalProperties.line.width = 20000
        
        # 添加到工作表
        chart_ws.add_chart(chart1, "A1")
        
        # 图表2: 资产价格对比图
        chart2 = LineChart()
        chart2.title = f"{asset1} vs {asset2} 标准化价格对比"
        chart2.style = 12
        chart2.y_axis.title = '标准化价格 (起始点=100)'
        chart2.x_axis.title = '日期'
        chart2.width = 20
        chart2.height = 12
        
        # 添加标准化价格数据
        price1_data = Reference(data_ws, min_col=5, min_row=1, max_col=5, max_row=max_row)
        price2_data = Reference(data_ws, min_col=6, min_row=1, max_col=6, max_row=max_row)
        
        chart2.add_data(price1_data, titles_from_data=True)
        chart2.add_data(price2_data, titles_from_data=True)
        chart2.set_categories(dates)
        
        # 设置系列样式
        chart2.series[0].graphicalProperties.line.solidFill = "0066CC"
        chart2.series[1].graphicalProperties.line.solidFill = "CC0000"
        chart2.series[0].graphicalProperties.line.width = 20000
        chart2.series[1].graphicalProperties.line.width = 20000
        
        # 添加到工作表
        chart_ws.add_chart(chart2, "A25")
        
        # 添加图表说明
        chart_ws['A49'] = f"图表说明："
        chart_ws['A50'] = f"1. 上图显示{window}天滚动相关系数的时间序列变化"
        chart_ws['A51'] = f"2. 下图显示两个资产的标准化价格走势对比"
        chart_ws['A52'] = f"3. 分析期间: {data_ws['A2'].value} 至 {data_ws[f'A{max_row}'].value}"
        chart_ws['A53'] = f"4. 数据点数量: {max_row-1} 个"
        chart_ws['A54'] = f"5. 相关系数范围: {results[corr_col].min():.3f} 至 {results[corr_col].max():.3f}"
        chart_ws['A55'] = f"6. 平均相关系数: {results[corr_col].mean():.3f}"
        
        # 设置说明文字格式
        for row in range(49, 56):
            chart_ws[f'A{row}'].font = Font(size=12, bold=True if row == 49 else False)
        
        # 保存工作簿
        wb.save(output_file)
        print("Excel原生图表添加完成")
        print(f"Excel原生图表已创建，结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"创建Excel图表时发生错误: {e}")
