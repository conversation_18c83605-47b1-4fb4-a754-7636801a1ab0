import pandas as pd
import os

def check_excel_file():
    """
    检查生成的Excel文件内容
    """
    excel_file = 'financial_analysis_results.xlsx'
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    print(f"✓ Excel文件存在: {excel_file}")
    print("=" * 60)
    
    # 读取Excel文件
    xl = pd.ExcelFile(excel_file)
    
    print(f"Excel文件包含 {len(xl.sheet_names)} 个工作表:")
    for i, sheet in enumerate(xl.sheet_names, 1):
        print(f"  {i}. {sheet}")
    
    print("\n" + "=" * 60)
    print("各工作表数据概览:")
    print("=" * 60)
    
    for sheet_name in xl.sheet_names:
        try:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            print(f"\n【{sheet_name}】")
            print(f"  数据形状: {df.shape}")
            print(f"  列名: {list(df.columns)}")
            
            # 显示前几行数据
            if len(df) > 0:
                print("  前3行数据:")
                print(df.head(3).to_string(index=False))
            else:
                print("  ⚠ 工作表为空")
                
        except Exception as e:
            print(f"  ❌ 读取工作表 '{sheet_name}' 时出错: {e}")
    
    print("\n" + "=" * 60)
    print("✓ Excel文件检查完成!")
    print("=" * 60)

if __name__ == "__main__":
    check_excel_file()
